<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Editor - MixCertificate</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/all.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.7.0/dropzone.min.css">
  <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/smoothness/jquery-ui.css" />
  <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css" />
  <link rel="stylesheet" href="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.contextMenu.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Alex+Brush&family=Crimson+Text&family=Great+Vibes&family=Lora&family=Pacifico&family=Roboto&family=Times+New+Roman&display=swap" rel="stylesheet">


  <!-- Include Tippy.js for tooltips -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tippy.js/6.3.7/themes/light-border.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/tippy.js/6.3.7/bundle.min.js"></script>

  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- html2canvas -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/0.5.0-beta4/html2canvas.min.js"></script>

  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />

  <script src="https://cdn.jsdelivr.net/npm/@melloware/coloris/dist/coloris.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/@melloware/coloris/dist/coloris.min.css" rel="stylesheet">

<!-- full screen starts -->
<style>
#fullScreenPreview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.8); /* To darken the background */
    justify-content: center;
    align-items: center;
}

#fullScreenCanvas {
    display: block;
    width: 100%;
    height: 100%;
}


</style>
<!-- full screen ends -->


<!-- canvas css starts -->
<style>
 .canvas-editor-main {
  width: 100%;
  height: 90vh;
  border: 1px solid #ccc;
  overflow: auto;
  transition: all 0.3s ease; /* Add smooth transition */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* Added for absolute positioning of canvas */
}
   #canvas-editor {
      display: block;
      transition: all 0.3s ease; /* Add smooth transition */
  }

  /* Canvas container expansion styles */
  .col-sm-12.col-md-5.col-lg-8 {
      transition: all 0.3s ease; /* Add smooth transition */
  }

  /* Ensure canvas container is centered in all states */
  .canvas-container {
      margin: 0 auto !important; /* Center the canvas */
  }

  /* Ensure canvas-editor-main has position relative for absolute positioning of canvas */
  .canvas-editor-main {
      position: relative !important;
  }

  /* Fixed layout styles */
  .fixed-layout {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .sidebar-column {
    flex: 0 0 auto;
    transition: all 0.3s ease;
    overflow-y: auto;
    max-height: 100vh;
  }

  .canvas-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .canvas-editor-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* When sidebar is collapsed */
  .sidebar-collapsed .sidebar-column {
    width: 0;
    overflow: hidden;
  }

  .sidebar-collapsed .canvas-column {
    flex: 1;
    width: 100%;
  }

  @media (max-width: 768px) {
      .canvas-editor-main {
          height: 80vh;
      }
  }
  </style>

<!-- canvas css ends -->

<!-- toast starts-->
 <style>
  .toast {
    position: fixed;
    top: 40vh;
    right: 18%;
    transform: translateX(-50%);
    z-index: 9999;
    /*opacity: 0;*/
    transition: opacity 0.5s ease-in-out;
}

.toast.show {
    opacity: 1;
}
 </style>
<!-- toast ends-->

<!-- crop button starts -->
 <style>/* General button styling for the crop toolbar */
  #image-crop-cropImageBtn {
      font-size: 18px;
      background-color: #6c757d; /* Secondary Bootstrap button color */
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      cursor: pointer;
  }

  #image-crop-cropImageBtn:hover {
      background-color: #5a6268; /* Darker secondary color on hover */
  }

  /* Confirm button initially hidden using d-none */
  #image-crop-confirmCropBtn {
      position: absolute;
      padding: 8px 12px;
      background-color: #6c757d; /* Secondary Bootstrap color */
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      z-index: 1000; /* Make sure it appears above canvas */
  }

  /* Show the confirm button below the cropping area */
  #image-crop-confirmCropBtn.show {
      display: block; /* Visible when cropping area is selected */
  }

  #image-crop-confirmCropBtn.d-none {
      display: none; /* Use Bootstrap's d-none class */
  }

  #image-crop-confirmCropBtn:hover {
      background-color: #5a6268; /* Darker secondary color on hover */
  }

  /* Toolbar styling for cropping */
  .image-crop-editor-toolbar {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 5px;
  }

  /* Sticky positioning for the toolbar at the bottom */
  .image-crop-sticky-bottom {
      position: sticky;
      bottom: 0;
      z-index: 500;
  }

  /* Optional: styling for crop rectangle */
  .image-crop-cropRect {
      border: 1px dashed black; /* Dashed border for the crop rectangle */
      background-color: rgba(0, 0, 0, 0.1); /* Semi-transparent fill */
  }

 </style>
<!-- crop button ends -->

<!-- Editor toolbar starts -->
<style>
  .editor-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    white-space: nowrap;
    overflow-x: hidden;
    max-width: 95%;
    scrollbar-width: thin;
    transition: all 0.3s ease;
    border: 2px solid #ddd;
  }

  /* More options button */
  .more-options-btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .more-options-btn:hover {
    background-color: #e9ecef;
  }

  /* Dropdown menu for additional options */
  .toolbar-dropdown {
    position: absolute;
    top: 45px; /* Position it just below the toolbar */
    left: 50%;
    transform: translateX(-50%);
    width: 95%;
    max-width: 95vw;
    background-color: white;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 8px;
    display: none;
    z-index: 1001; /* Higher than toolbar */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scrollbar-width: thin; /* For Firefox */
  }

  /* Style the scrollbar for the dropdown */
  .toolbar-dropdown::-webkit-scrollbar {
    height: 6px;
    background-color: #f5f5f5;
  }

  .toolbar-dropdown::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }

  .toolbar-dropdown.show {
    display: block !important;
  }

  /* Style for groups inside dropdown */
  .toolbar-dropdown .editor-toolbar-group {
    display: inline-flex;
    align-items: center;
    margin-bottom: 8px;
    margin-right: 8px;
    flex-wrap: wrap;
  }

  /* Container for all groups to allow wrapping */
  .toolbar-dropdown {
    display: none;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
  }

  /* On smaller screens, stack items vertically */
  @media (max-width: 768px) {
    .toolbar-dropdown {
      flex-direction: column;
      align-items: flex-start;
      white-space: normal;
      overflow-x: hidden;
    }
  }

  /* Make dropdown items smaller */
  .toolbar-dropdown .btn,
  .toolbar-dropdown .form-control,
  .toolbar-dropdown input[type="number"],
  .toolbar-dropdown select {
    padding: 2px 6px;
    font-size: 0.875rem;
    height: auto;
    min-height: 30px;
  }

  .toolbar-dropdown .color-picker-wrapper {
    margin-right: 4px;
  }

  .toolbar-dropdown input[type="range"] {
    width: 80px;
  }

  /* Make Material Icons black */
  .material-icons {
    color: #000;
  }

  /* Additional styles to ensure dropdown is visible */
  .toolbar-dropdown.show {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  /* Style the more options button to indicate active state */
  #moreOptionsBtn.active {
    background-color: #e9ecef;
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
  }

  /* Make sure the toolbar is visible when not having d-none class */
  .editor-toolbar:not(.d-none) {
    display: flex !important;
  }

  .editor-toolbar::-webkit-scrollbar {
    height: 4px;
  }

  .editor-toolbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  .editor-toolbar .form-control {
    max-width: 80px;
    width: auto;
    height: 32px;
    font-size: 12px;
  }

  .editor-toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 6px;
    border-right: 1px solid #eee;
  }

  .editor-toolbar-group:last-child {
    border-right: none;
  }

  .editor-toolbar .btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    color: #555;
    border: none;
  }

  .editor-toolbar .btn:hover {
    background-color: #f5f5f5;
  }

  .editor-toolbar .btn.active {
    background-color: #f0f0f0;
    color: #000;
  }

  .editor-toolbar .material-icons {
    font-size: 18px;
    color: #555;
  }

  .editor-toolbar .btn:hover .material-icons {
    color: #000;
  }

  .clr-swatches button {
    border-radius: 50%;
    height: 1rem;
    width: 1rem;
  }

  /* Native color input styling */
  .native-color-input {
    width: 32px;
    height: 32px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    overflow: hidden;
  }

  /* Style the color swatch */
  .native-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
  }

  .native-color-input::-webkit-color-swatch {
    border: none;
    border-radius: 5px;
  }

  .native-color-input::-moz-color-swatch {
    border: none;
    border-radius: 5px;
  }

  /* Removed Coloris-related CSS */

  /* Removed more Coloris-related CSS */

  /* Removed more Coloris-related CSS */

  /* Removed more Coloris-related CSS */

  /* Removed more Coloris-related CSS */

  /* Removed more Coloris-related CSS */

  /* Removed all Coloris-related CSS */

  /* Color picker tooltip */
  .editor-toolbar-group > div {
    position: relative;
    display: inline-block;
    margin: 0 2px;
  }

  /* Color picker wrapper and label */
  .color-picker-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 4px;
    position: relative;
  }

  .color-label {
    font-size: 10px;
    color: #666;
    margin-bottom: 2px;
    text-transform: uppercase;
    font-weight: 500;
  }

  /* Adjust spacing in the toolbar */
  .editor-toolbar-group:first-child {
    padding-right: 10px;
    border-right: 1px solid #eee;
  }

  /* This rule was duplicated and has been removed */

  #opacity {
    width: 80px;
    height: 6px;
  }

  .d-none {
    display: none !important;
  }
</style>

<!-- Editor toolbar ends -->

  <!-- elements with border starts -->
   <style>
     .shape-toolbar .btn i {
        line-height: 60px; /* Center icon vertically */
    }
   </style>
  <!-- elements with border ends -->


  <!-- qr code starts -->
  <style>
    .qr-code-container {
        text-align: center;
        margin-top: 20px;
    }

    .image-item {
        max-width: 150px;
        cursor: pointer;
    }
</style>
  <!-- qr code ends -->

  <!-- pixabay search starts -->
  <style>
    .icon-item {

        cursor: pointer;
    }
    .image-item {
        width: 150px;
        margin: 10px;
        cursor: pointer;
    }
    #searchResults {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    .search-bar {
        max-width: 500px;
        margin: 20px auto;
    }
    .loading-spinner {
        text-align: center;
        margin-top: 20px;
    }
</style>
  <!-- pixabay search ends -->


  <!-- editor sidebar + content starts -->
  <style>
    .editor-main-sidebar {
      background-color: #18191b;
      height: 100vh;
      overflow: hidden;
      scrollbar-width: none;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1050;
      /* For Firefox */
      transition: scrollbar-width 0.5s ease;
      /* Smooth transition */
    }

    .editor-main-sidebar.hide-scrollbar {
      scrollbar-width: none;
      /* For Firefox */
    }

    .editor-main-sidebar::-webkit-scrollbar {
      width: 0;
      /* Hide scrollbar for WebKit browsers */
      display: none;
    }

    .editor-main-sidebar.hide-scrollbar::-webkit-scrollbar {
      width: 0;
      /* Hide scrollbar for WebKit browsers */
    }

    .sidebar-text {
      font-size: 12px !important;
      font-weight: 500;
      color: #f1f1f1;
    }

    .material-icons {
      color: #f1f1f1;
    }

    .editor-sidebar i {
      color: #f1f1f1;
      /* background-color: black; */
    }

    /* Fixed sidebar styles */
    #content-sidebar {
      position: fixed;
      top: 5.5rem; /* Match the margin-top from the parent container */
      left: 0;
      height: calc(100vh - 5.5rem);
      overflow-y: auto; /* Allow vertical scrolling */
      overflow-x: hidden; /* Prevent horizontal scrolling */
      z-index: 1040;
      width: 7%; /* col-2 width */
    }

    /* Content sidebar area */
    #v-pills-tabContent {
      position: fixed;
      top: 5.5rem;
      left: 6%; /* Same as content-sidebar width */
      height: calc(100vh - 5.5rem);
      overflow-y: auto; /* Allow vertical scrolling */
      overflow-x: hidden; /* Prevent horizontal scrolling */
      z-index: 1040;
      width: 25%; /* col-3 width */
      background-color: #18191b; /* Match the sidebar background */
    }

    /* Ensure tab panes don't overflow horizontally but allow vertical scrolling */
    #v-pills-tabContent .tab-pane {
      height: 100%;
      overflow-x: hidden;
      overflow-y: visible;
    }

    /* Content sidebar closer */
    #content-sidebar-closer {
      position: fixed;
      top: 50%;
      left: 31.666%; /* content-sidebar + v-pills-tabContent width */
      transform: translateY(-50%);
      z-index: 1045;
    }

    /* Adjust main content area to account for fixed sidebars */
    .col-sm-12.col-md-5.col-lg-8 {
      margin-left: 34.666%;
    }

    /* Prevent horizontal scrolling on the entire page */
    html, body {
      overflow-x: hidden;
      width: 100%;
      max-width: 100%;
    }

    /* Header styles */
    .fixed-top {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      z-index: 1060;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    /* Responsive layout for smaller screens */
    @media (max-width: 992px) {
      #content-sidebar {
        position: fixed;
        top: 5rem;
        left: 0;
        width: 15%;
        z-index: 1050;
      }

      #v-pills-tabContent {
        position: fixed;
        top: 5rem;
        left: 15%;
        width: 85%;
        z-index: 1040;
      }

      #content-sidebar-closer {
        left: 95%;
      }

      .col-sm-12.col-md-5.col-lg-8 {
        margin-left: 0;
        margin-top: calc(100vh - 5.5rem);
        width: 100%;
      }
    }

    /* For mobile screens, stack everything vertically */
    @media (max-width: 768px) {
      /* Keep header fixed on small screens */
      .fixed-top {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 1060;
      }

      /* Adjust header content for small screens */
      .fixed-top {
        padding: 5px 10px;
      }

      .fixed-top .navbar-brand span {
        display: none;
      }

      .fixed-top #designName {
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* Make buttons smaller on mobile */
      .fixed-top .btn {
        padding: 4px 8px;
        font-size: 0.8rem;
      }

      /* Fix editor toolbar positioning on mobile */
      .editor-toolbar {
        position: fixed;
        top: auto;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        max-width: 90%;
        z-index: 1070;
        border: 2px solid #ddd;
        background-color: rgba(255, 255, 255, 0.95);
      }

      /* Make toolbar items smaller on mobile */
      .editor-toolbar .btn,
      .editor-toolbar .form-control,
      .editor-toolbar input[type="color"] {
        padding: 2px 4px;
        font-size: 0.8rem;
      }

      /* Style the more options button to be more noticeable on mobile */
      #moreOptionsBtn {
        background-color: #007bff;
        color: white;
        padding: 4px 8px;
      }

      #moreOptionsBtn .material-icons {
        color: white;
      }

      #moreOptionsBtn.active {
        background-color: #0056b3;
        box-shadow: inset 0 3px 5px rgba(0,0,0,.3);
      }

      /* Position dropdown above the toolbar on mobile */
      .toolbar-dropdown {
        position: fixed;
        top: auto;
        bottom: 70px; /* Position above the toolbar */
        left: 50%;
        transform: translateX(-50%);
        width: 90%;
        max-height: 50vh; /* Increased height to accommodate stacked items */
        overflow-y: auto;
        overflow-x: hidden; /* No horizontal scrolling needed */
        z-index: 1080; /* Higher than toolbar */
        background-color: rgba(255, 255, 255, 0.98); /* More opaque */
        border: 2px solid #007bff; /* Highlight border to make it stand out */
        border-radius: 8px;
        box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.2); /* Shadow for better visibility */
        padding: 10px 15px;
        white-space: normal; /* Allow text to wrap */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        scrollbar-width: thin; /* For Firefox */
        flex-direction: column;
        align-items: stretch;
      }

      /* Improve scrollbar visibility on mobile */
      .toolbar-dropdown::-webkit-scrollbar {
        width: 8px; /* Vertical scrollbar width */
        background-color: rgba(245, 245, 245, 0.5);
      }

      .toolbar-dropdown::-webkit-scrollbar-thumb {
        background-color: rgba(0, 123, 255, 0.5);
        border-radius: 4px;
      }

      /* Ensure dropdown is visible when shown */
      .toolbar-dropdown.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      /* Adjust toolbar groups in dropdown for mobile - stack vertically */
      .toolbar-dropdown .editor-toolbar-group {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        margin-right: 0;
        padding-right: 0;
        border-right: none;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eee;
      }

      .toolbar-dropdown .editor-toolbar-group:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      /* Group label for better organization */
      .toolbar-dropdown .editor-toolbar-group::before {
        content: attr(data-group-name);
        display: block;
        width: 100%;
        font-size: 12px;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 6px;
        text-transform: uppercase;
      }

      /* Ensure buttons and controls are properly sized and spaced */
      .toolbar-dropdown .btn,
      .toolbar-dropdown .form-control,
      .toolbar-dropdown select,
      .toolbar-dropdown input {
        margin: 3px;
      }



      /* Mobile tap overlay for closing dropdown */
      #mobile-tap-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.3);
        z-index: 1070;
        display: none;
      }

      #mobile-tap-overlay.active {
        display: block;
      }

      /* No extra padding needed at the top since we're not using a close button */
      .toolbar-dropdown {
        padding-top: 8px;
      }

      /* Hide some buttons on very small screens */
      @media (max-width: 576px) {
        .fixed-top #previewBtn,
        .fixed-top .dropdown:not(:first-of-type) {
          display: none;
        }
      }

      /* Stack sidebar and content vertically */
      #content-sidebar,
      #v-pills-tabContent {
        position: relative;
        width: 100%;
        left: 0;
        height: auto;
        min-height: 50vh;
      }

      #content-sidebar {
        top: 5rem;
        z-index: 1050;
      }

      #v-pills-tabContent {
        top: 5rem;
        z-index: 1040;
      }

      #content-sidebar-closer {
        display: none;
      }

      .col-sm-12.col-md-5.col-lg-8 {
        margin-left: 0;
        margin-top: 0;
        width: 100%;
      }
    }

    .editor-sidebar .active {
      /* color: white; */
      background-color: #212529;
    }

    .draggable {
      cursor: move;
    }


    #textColor,
    #bgColor {
      width: 50px;
      height: 15px;
      /* Set a manageable height */
      background-color: linear-gradient(to right, red, yellow, aqua, green);
      padding: 0;
      border: none;
      outline: none;
      transform: scale(0.5);
      /* Scale down the size */
      transform-origin: left;
    }


/* SIDEBAR TAB STARTS */

    /* Sidebar tab starts */
    .tab-content {
      height: 100vh;
      overflow-y: scroll;
      scrollbar-width: thin;
      /* For Firefox */
      transition: scrollbar-width 0.5s ease;
      /* Smooth transition */
    }

    .tab-content.hide-scrollbar {
      scrollbar-width: none;
      /* For Firefox */
    }

    .tab-content::-webkit-scrollbar {
      width: 8px;
      /* Default scrollbar width */
      transition: width 0.5s ease;
      /* Smooth transition */
    }

    .tab-content.hide-scrollbar::-webkit-scrollbar {
      width: 0;
      /* Hide scrollbar for WebKit browsers */
    }
    .editor-sidebar-menu-items {
  transition: background 0.3s ease, transform 0.2s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.editor-sidebar-menu-items:hover {
  background: linear-gradient(to right, #d3d3d3, #b0b0b0); /* Soft gray gradient */
  color: #000; /* Dark text for contrast */
  transform: scale(1.03);
  z-index: 2;
}

.editor-sidebar-menu-items:hover .material-icons,
.editor-sidebar-menu-items:hover .sidebar-text {
  color: #000;
}


    /* SIDEBAR TAB ENDS */




    /* EDITOR TOOLBAR STARTS  */
    .opacity-slider {
      width: 200px;
      /* Adjust the width as needed */
      top: 100%;
      /* Position below the button */
      left: 0;
      /* Align left with the button */
      z-index: 1000;
      /* Ensure it appears above other elements */
    }

    .opacity-slider .form-range {
      background-color: #eee;
      height: 1rem !important;
    }

    .opacity-slider .form-range input[type="value"] {
      background-color: red !important;
    }

    .opacity-slider input[type="number"] {
      width: 70px;
      /* text-align: center; */
    }

    /* EDITOR TOOLBAR STARTS  */



/* STICKY FOOTER STARTS  */
    .sticky-footer {
      position: absolute;
      bottom: 0;
      width: 100%;
      background-color: #f8f9fa;
      /* Matches the light background in the image */
    }

    #notesInputBox {
      width: 250px;
      background-color: #fff;
    }

    #expandBtn {
      background-color: #fff;
      /* Matches the button color in the image */
      color: #343a40;
      /* Dark color for icons */
    }

    .progress-bar {
      background-color: #007bff;
      /* Blue color for the progress bar */
    }

    /* Sticky Toolbar and Footer */
    .sticky-top {
      position: sticky;
      top: 0;
      z-index: 1020;
    }

    .sticky-bottom {
    position: sticky;
    bottom: 0;
    z-index: 500;
    background-color: #fff; /* Ensure background color to prevent overlap issues */
    padding: 10px;
    border-top: 1px solid #ddd;
    width: 100%; /* Ensure full width */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); /* Optional shadow for effect */
}

    /* STICKY FOOTER ENDS  */



    /* DOWNLOAD BUTTONS STARTS */
    #downloadBtn {
      background-color: #007bff;
      /* Primary color */
      color: white;
    }

    #downloadBtn:hover {
      background-color: #0056b3;
      /* Darker shade on hover */
    }

    .btn-outline-success {
      color: #28a745;
      border-color: #28a745;
    }

    .btn-outline-success:hover {
      background-color: #28a745;
      color: white;
    }

    /* DOWNLOAD BUTTONS ENDS */


/* FILE UPLOADS STARTS  */

  .image-container {
    position: relative;
    display: inline-block;
    margin: 5px;
  }

  .image-container img {
    max-width: 150px;
    height: auto;
    display: block;
  }

  .image-buttons {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .image-container:hover .image-buttons {
    opacity: 1;
  }

  .upload-image-btn {
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 5px;
    margin: 2px;
    cursor: pointer;
  }

  .upload-image-btn:hover {
    background: rgba(0, 0, 0, 0.8);
  }
/* FILE UPLOADS ENDS */
</style>
<!-- editor sidebar + content ends -->



</head>

<body>
  <div class="container-fluid m-0 p-0">


  <!-- Top Menu Starts -->
  <div class="d-flex align-items-center p-3 shadow-sm fixed-top bg-white">


    <!-- Logo Component -->
    <a href="/designs/" class="navbar-brand d-flex align-items-center">
      <img src="/assets/images/logo-sm.svg" alt="Logo" width="30" height="30" class="me-2" title="mixcertificate">
      <span class="fw-bold d-none">MixCertificate</span>
    </a>

          <!-- Invisible input for design name -->
          <input type="text" id="designName" placeholder="Design Name" value="Untitle Design" class="form-control me-2"
            style="border: none; background-color: transparent; height: 50px; line-height: 50px; color: gray; text-align: left; outline: none;"
            onfocus="this.style.border='none'" onblur="this.style.border='none'" />

          <!-- Save Button -->
          <button id="saveCanvas"  class="btn btn-outline-secondary me-2 d-flex align-items-center">
            <i class="fas fa-save me-2"></i>Save
          </button>
          <button id="previewBtn" class="btn btn-outline-secondary me-2 d-flex align-items-center">
            <i class="fas fa-eye me-2"></i>Preview
          </button>

          <!-- HQ download button starts -->
          <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle m-2" type="button" id="downloadDropdown" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-download me-2"></i>Download
            </button>
            <ul class="dropdown-menu" aria-labelledby="downloadDropdown">
                <li>
                    <a class="dropdown-item" id="downloadPngBtn" href="#">Download PNG</a>
                </li>
                <li>
                    <a class="dropdown-item" id="downloadJpegBtn" href="#">Download JPEG</a>
                </li>
                <li>
                    <a class="dropdown-item" id="downloadPdfBtn" href="#">Download PDF</a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li class="dropdown-header">For Print (300 DPI)</li>
                <li>
                    <a class="dropdown-item" id="printDownloadPngBtn" href="#">Download PNG</a>
                </li>
                <li>
                    <a class="dropdown-item" id="printDownloadJpegBtn" href="#">Download JPEG</a>
                </li>
                <li>
                    <a class="dropdown-item" id="printDownloadPdfBtn" href="#">Download PDF</a>
                </li>
            </ul>
        </div>
      <!-- HQ download button ends -->


          <!-- Dropdown button with icons and options -->
          <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton"
              data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-plus me-2"></i>Create
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
              <li>
                <a class="dropdown-item" href="#" data-type="horizontal" onclick="openEditor('horizontal')">
                  <i class="fas fa-arrows-alt-h me-2"></i> Horizontal Certificate
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" data-type="vertical" onclick="openEditor('vertical')">
                  <i class="fas fa-arrows-alt-v me-2"></i> Vertical Certificate
                </a>
              </li>
              <li>
                <a class="dropdown-item" href="#" data-type="badge" onclick="openEditor('badge')">
                  <i class="fas fa-badge me-2"></i> Badge
                </a>
              </li>
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <div class="px-3 py-2">
                  <input type="number" id="customWidth" class="form-control mb-2" placeholder="Width (px)" />
                  <input type="number" id="customHeight" class="form-control" placeholder="Height (px)" />
                  <button class="btn btn-primary mt-2" onclick="createCustomDesign()">Create</button>
                </div>
              </li>
            </ul>
          </div>
        </div>
  <!-- Top Menu Ends -->



  <!-- Sidebar Column starts -->
    <div style="margin: 0; margin-top: 5rem;" class="row border-0 p-0">


      <div class="col-lg-4">

        <div class="row">

          <div id="content-sidebar" class="col-3 col-md-2 col-lg-2 p-0 editor-main-sidebar">


            <div class="nav flex-column nav-menu-container editor-sidebar" id="v-pills-tab" role="tablist"
              aria-orientation="vertical">

              <button class="editor-sidebar-menu-items active rounded-0 btn" id="v-pills-home-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-home" type="button" role="tab" aria-controls="v-pills-home" aria-selected="true">
                <span class="material-icons">home</span>
                <div class="fs-6 sidebar-text">Design</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-badges-tab" data-bs-toggle="pill"
              data-bs-target="#v-pills-badges" type="button" role="tab" aria-controls="v-pills-badges"
              aria-selected="false">
              <span class="material-icons">verified</span>
              <div class="fs-6 sidebar-text">Badges</div>
            </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-elements-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-elements" type="button" role="tab" aria-controls="v-pills-elements"
                aria-selected="false">
                <span class="material-icons">category</span>
                <div class="fs-6 sidebar-text">Elements</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-text-tab" data-bs-toggle="pill" data-bs-target="#v-pills-text"
                type="button" role="tab" aria-controls="v-pills-text" aria-selected="false">
                <span class="material-icons">text_fields</span>
                <div class="fs-6 sidebar-text">Text</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-brand-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-brand" type="button" role="tab" aria-controls="v-pills-brand"
                aria-selected="false">
                <span class="material-icons">branding_watermark</span>
                <div class="fs-6 sidebar-text">Brand</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-image-upload-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-image-upload" type="button" role="tab" aria-controls="v-pills-image-upload"
                aria-selected="false">
                <span class="material-icons">cloud_upload</span>
                <div class="fs-6 sidebar-text">Uploads</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-frames-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-frames" type="button" role="tab" aria-controls="v-pills-frames"
                aria-selected="false">
                <span class="material-icons">crop</span>
                <div class="fs-6 sidebar-text">Frames</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-grids-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-grids" type="button" role="tab" aria-controls="v-pills-grids"
                aria-selected="false">
                <span class="material-icons">grid_on</span>
                <div class="fs-6 sidebar-text">QR Code</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-backgrounds-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-backgrounds" type="button" role="tab" aria-controls="v-pills-backgrounds"
                aria-selected="false">
                <span class="material-icons">wallpaper</span>
                <div class="fs-6 sidebar-text" alt="pixabay">Photos</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-shapes-elements-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-shapes-elements" type="button" role="tab" aria-controls="v-pills-shapes-elements"
                aria-selected="false">
                <span class="material-icons">view_module</span>
                <div class="fs-6 sidebar-text">Icons</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-variable-gifs-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-variable-gifs" type="button" role="tab" aria-controls="v-pills-variable-gifs"
                aria-selected="false">
                <span class="material-icons">insert_emoticon</span>
                <div class="fs-6 sidebar-text">Variables</div>
              </button>

              <button class="py-3 rounded-0 btn editor-sidebar-menu-items" id="v-pills-stickers-gifs-tab" data-bs-toggle="pill"
                data-bs-target="#v-pills-stickers-gifs" type="button" role="tab" aria-controls="v-pills-stickers-gifs"
                aria-selected="false">
                <span class="material-icons">insert_emoticon</span>
                <div class="fs-6 sidebar-text">Stickers & GIFs</div>
              </button>


            </div>
          </div>

          <!-- Content sidebarArea starts-->
          <div class="col-9 col-md-8 col-lg-8 tab-content content-container bg-dark" id="v-pills-tabContent">
            <div class="tab-pane fade show active text-light" id="v-pills-home" role="tabpanel" aria-labelledby="v-pills-home-tab" tabindex="0">

              <h5 class="text-light p-2 pt-4">Certificate Backgrounds</h5>

              <div class="">
                <!-- Search Bar -->
                <div class="search-bar">
                    <div class="input-group mb-3">
                        <input type="text" id="designSearchQuery" class="form-control" placeholder="Search Images" value="certificate">
                        <button class="btn btn-secondary" id="designSearchButton">Search</button>
                    </div>
                </div>


              <!-- Container for result rendering images -->
              <div id="designSearchResults" class="mt-4">
                <!-- Search templates will be appended here -->
              </div>


            </div>

            </div>

            <div class="tab-pane fade text-light" id="v-pills-elements" role="tabpanel" aria-labelledby="v-pills-elements-tab"
              tabindex="0">

              <h5 class="text-light pt-4">Elements</h5>

              <!-- element content starts -->

                    <!-- Shape Toolbar -->
                    <div id="shape-toolbar" class="mt-3" role="" aria-label="Shape Toolbar">
                      <button id="addRectangleBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons">rectangle</span>
                      </button>
                      <button id="addCircleBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons">circle</span>
                      </button>
                      <button id="addTriangleBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons">change_history</span>
                      </button>
                      <button id="addEllipseBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons" heigt="20px">circle</span>
                      </button>
                      <button id="addLineBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons">remove</span>
                      </button>
                      <button id="addPolygonBtn" type="button" class="btn pt-0 mb-1 btn-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <span class="material-icons"><i class="fa-solid fa-square"></i></span>
                      </button>
                    </div>


                    <!-- Shape Toolbar with Borders -->
                    <div id="shape-toolbar" class="mt-2" role="" aria-label="Shape Toolbar">
                      <button id="addRectangleBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-primary" style="font-size: 36px; width: 60px; height: 60px;">
                        <span class="material-icons">rectangle</span>
                      </button>
                      <button id="addCircleBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-secondary" style="font-size: 36px; width: 60px; height: 60px;">
                          <i class="fas fa-circle"></i>
                      </button>
                      <button id="addTriangleBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-success" style="font-size: 36px; width: 60px; height: 60px;">
                          <i class="fas fa-caret-up"></i>
                      </button>
                      <button id="addEllipseBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-info" style="font-size: 36px; width: 60px; height: 60px;">
                          <i class="fas fa-circle"></i> <!-- Use as a substitute for ellipse -->
                      </button>
                      <button id="addLineBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-warning" style="font-size: 36px; width: 60px; height: 60px;">
                          <i class="fas fa-minus"></i>
                      </button>
                      <button id="addPolygonBorderedBtn" type="button" class="pt-1 btn mb-1 btn-outline-danger" style="font-size: 36px; width: 60px; height: 60px;">
                          <i class="fas fa-square"></i>
                      </button>
                    </div>

              <!-- element content ends -->
            </div>



              <!-- Text tab content -->
              <div  class="tab-pane fade text-light" id="v-pills-text"
              role="tabpanel" aria-labelledby="v-pills-text-tab" tabindex="0">

                <div class="mt-3 text-list">
                  <h5 class="text-light p-2">Select text style</h5>

                  <div class="d-grid gap-2 m-1 text-light">
                    <button class="btn text-light border m-1 fs-1" id="addH1" data-header="h1">
                      Header 1
                    </button>
                    <button class="btn text-light border fs-2" id="addH2" data-header="h2">
                      Header 2
                    </button>
                    <button class="btn text-light border fs-3" id="addH3" data-header="h3">
                      Header 3
                    </button>
                    <button class="btn text-light border fs-4" id="addH4" data-header="h4">
                      Header 4
                    </button>
                    <button class="btn text-light border fs-5" id="addH5" data-header="h5">
                      Header 5
                    </button>
                    <button class="btn text-light border m-1" id="addpara" data-header="p">
                      Paragraph
                    </button>
                    <div id="fontList" class="d-flex flex-wrap">
                      <!-- Font buttons will be appended here -->
                    </div>
                  </div>
                </div>
              </div>


            <!-- brand section starts -->
            <div class="tab-pane fade text-light" id="v-pills-brand"
            role="tabpanel" aria-labelledby="v-pills-brand-tab" tabindex="0">
              Brand content...
            </div>
            <!-- brand section ends -->

            <!-- Dropzone area for uploading files -->
            <div class="tab-pane fade m-2" id="v-pills-image-upload" role="tabpanel" aria-labelledby="v-pills-image-upload-tab" tabindex="0">
              <form action="/api/files/upload/" class="dropzone" id="imageDropzone">
                <div class="dz-message">
                  <span class="material-icons me-2 text-secondary">upload</span>
                  <div>Drag or click to upload</div>
                </div>
              </form>

              <!-- Container for uploaded images -->
              <div id="uploadedImages" class="mt-4">
                <!-- Uploaded images will be appended here -->
              </div>
            </div>

            <div class="tab-pane fade text-light" id="v-pills-frames" role="tabpanel" aria-labelledby="v-pills-frames-tab"
              tabindex="0">
              Frames content...
            </div>
            <div class="tab-pane fade text-light" id="v-pills-grids" role="tabpanel" aria-labelledby="v-pills-grids-tab" tabindex="0">


            <!-- QR Code Generator Section -->
            <div class="container my-4">
              <div class="row">
                  <div class="col-md-12">
                      <div class="input-group mb-3">
                          <input type="text" id="website" class="form-control" placeholder="Enter URL here" aria-label="Enter URL here">
                          <button id="generateQrBtn" class="btn btn-secondary">Generate</button>
                      </div>
                      <div id="qrCodeContainer" class="text-center">
                          <!-- QR Code image will be appended here -->
                      </div>
                  </div>
              </div>
            </div>



            </div>

            <div class="tab-pane fade show m-4" id="v-pills-backgrounds" role="tabpanel" aria-labelledby="v-pills-backgrounds-tab" tabindex="0">

              <div class="">
                <!-- Search Bar -->
                <div class="search-bar">
                    <div class="input-group mb-3">
                        <input type="text" id="searchQuery" class="form-control" placeholder="Search Images" value="peace">
                        <button class="btn btn-secondary" id="searchBtn">Search</button>
                    </div>
                </div>

                <!-- Search Results -->
                <div id="searchResults" class="d-flex justify-content-center align-items-center">
                    <!-- Images will be loaded here -->
                </div>
            </div>



            </div>
            <div class="tab-pane fade text-light" id="v-pills-shapes-elements" role="tabpanel"
              aria-labelledby="v-pills-shapes-elements-tab" tabindex="0">

              <div class="">
                <div class="search-bar input-group mb-3">
                    <input type="text" id="searchIconQuery" class="form-control" value="medal" placeholder="Search">
                    <button id="searchIconBtn" class="btn btn-secondary">Search</button>
                </div>
                <div id="iconResults" class="d-flex flex-wrap"></div>
                <div id="iconLoading" class="d-none">
                    <p>Loading...</p>
                </div>
            </div>



            </div>

            <!-- variable content starts -->
            <div class="tab-pane fade text-light" id="v-pills-stickers-gifs" role="tabpanel"
              aria-labelledby="v-pills-stickers-gifs-tab" tabindex="0">

              Stickers & GIFs content...


            </div>



            <div class="tab-pane fade text-light" id="v-pills-variable-gifs" role="tabpanel"
              aria-labelledby="v-pills-variable-gifs-tab" tabindex="0">
              <div class="mt-4">
                <!-- Search Bar -->
                <div class="row">
                  <div class="">
                    <input type="text" id="search" class="form-control search-bar" placeholder="Search...">
                  </div>
                </div>

                <div class="row mb-5">
                  <!-- Left: Components List -->
                  <div class="">
                    <div id="components-list">
                      <!-- Predefined Variables will be dynamically added here -->
                    </div>

                    <!-- Add Variable Button -->
                    <button class="btn btn-secondary mb-5" id="add-variable-btn">Add New</button>
                  </div>
                </div>
              </div>
            </div>


            <div class="tab-pane fade text-light" id="v-pills-badges" role="tabpanel" aria-labelledby="v-pills-badges-tab"
              tabindex="0">
              Badges content...
            </div>
            <div class="tab-pane fade text-light" id="v-pills-photo-effects-filters" role="tabpanel"
              aria-labelledby="v-pills-photo-effects-filters-tab" tabindex="0">
              Photo Effects & Filters content...
            </div>
            <div class="tab-pane fade text-light" id="v-pills-layer-management" role="tabpanel"
              aria-labelledby="v-pills-layer-management-tab" tabindex="0">
              Layer Management content...
            </div>
            <div class="tab-pane fade text-light" id="v-pills-transparency-control" role="tabpanel"
              aria-labelledby="v-pills-transparency-control-tab" tabindex="0">
              Transparency Control content...
            </div>
            <div class="tab-pane fade text-light" id="v-pills-alignment-guides" role="tabpanel"
              aria-labelledby="v-pills-alignment-guides-tab" tabindex="0">
              Alignment & Guides content...
            </div>
          </div>
          <!-- Content sidebarArea starts-->

          <!-- button to close the content -->
          <div class="content-container col-lg-1 pl-0">
            <!-- Sidebar content goes here -->
            <div class="sidebar-content">
              <!-- Your sidebar elements or content -->
            </div>
          </div>

          <!-- Close button for collapsing sidebar (moved outside for fixed positioning) -->
          <div id="content-sidebar-closer" class="pl-0 ml-0">
            <button id="closeSidebarBtn" class="bg-dark border-0 pt-1">
              <span class="material-icons">chevron_left</span>
            </button>
          </div>

          <!-- Button to reopen sidebar when collapsed (initially hidden) -->
          <div id="reopen-sidebar-btn" class="d-none position-fixed" style="top: 10px; left: 10px; z-index: 1050;">
            <button id="openSidebarBtn" class="bg-dark border-0 pt-1 rounded">
              <span class="material-icons">chevron_right</span>
            </button>
          </div>


      </div>
    </div>
   <!-- Sidebar Column ends -->



    <!-- canvas editor starts -->
      <div class="col-sm-12 col-md-5 col-lg-8" style="transition: all 0.3s ease; display: flex; flex-direction: column; align-items: center;">
   <!-- New editor toolbar starts -->



        <!-- New editor toolbar ends -->


        <!-- Canvas Area with Toolbar -->
        <div class="position-relative" style="margin-top: 25px; width: 100%;">
          <!-- Editor Toolbar (moved inside the canvas container for proper positioning) -->
          <!-- Main Toolbar -->
          <div  class="editor-toolbar text-dark d-none">
            <!-- Main Toolbar - Essential Options -->

            <!-- Colors Group (Keep in main toolbar) -->
            <div class="editor-toolbar-group">
              <div title="Background color" class="color-picker-wrapper">
                <span class="color-label">BG</span>
                <input id="bgColorPicker" type="color" value="#ffffff" class="native-color-input" title="Background color">
              </div>
              <div title="Font color" class="color-picker-wrapper">
                <span class="color-label">Text</span>
                <input id="fontColorPicker" type="color" value="#333333" class="native-color-input" title="Font color">
              </div>
              <div title="Fill color" class="color-picker-wrapper">
                <span class="color-label">Fill</span>
                <input type="color" id="elementFillPicker" value="#3498db" class="native-color-input" title="Shape fill color">
              </div>
              <div title="Border color" class="color-picker-wrapper">
                <span class="color-label">Border</span>
                <input id="borderColorPicker" type="color" value="#000000" class="native-color-input" title="Border color">
              </div>
            </div>

            <!-- Text Formatting (Keep in main toolbar) -->
            <div class="editor-toolbar-group">
              <button id="boldBtn" class="btn btn-light" title="Bold"><i class="material-icons">format_bold</i></button>
              <button id="italicBtn" class="btn btn-light" title="Italic"><i class="material-icons">format_italic</i></button>
              <button id="underlineBtn" class="btn btn-light" title="Underline"><i class="material-icons">format_underlined</i></button>
              <button id="strikethroughBtn" class="btn btn-light" title="Strikethrough"><i class="material-icons">format_strikethrough</i></button>
            </div>




            <div class="editor-toolbar-group">
              <select id="fontFamily" class="form-control editor-font" title="Font family">
                <option value="Arial">Arial</option>
                <option value="Times New Roman">Times</option>
                <option value="Georgia">Georgia</option>
              </select>
              <input type="number" id="fontSizeInput" class="form-control" min="8" value="14" title="Font size">
            </div>



            <!-- More Options Button -->
            <div class="editor-toolbar-group">
              <button id="moreOptionsBtn" class="more-options-btn" title="More options">
                <i class="material-icons">more_horiz</i>
              </button>
            </div>
          </div>

          <!-- Dropdown for Additional Options (Outside the toolbar) -->
          <div class="toolbar-dropdown" id="toolbarDropdown">
            <!-- History (Moved from main toolbar) -->
            <div class="editor-toolbar-group" data-group-name="History">
              <button id="undoButton" class="btn btn-light" title="Undo"><i style="color: black;" class="material-icons">undo</i></button>
              <button id="redoButton" class="btn btn-light" title="Redo"><i style="color: black;" class="material-icons">redo</i></button>
            </div>

            <!-- Element Controls -->
            <div class="editor-toolbar-group" data-group-name="Element Controls">
              <button id="lockBtn" class="btn btn-light" title="Lock element"><i style="color: black;" class="material-icons">lock</i></button>
              <button id="unlockBtn" class="btn btn-light" title="Unlock element"><i style="color: black;" class="material-icons">lock_open</i></button>
              <button id="addLinkBtn" class="btn btn-light" title="Add link"><span style="color: black;" class="material-icons">link</span></button>
              <input id="opacity" type="range" min="0" max="1" step="0.1" value="1" title="Opacity">
            </div>

            <!-- Border Options -->
            <div class="editor-toolbar-group" data-group-name="Border Options">
              <input id="borderThickness" type="number" min="0" value="1" class="form-control" title="Border thickness" placeholder="Width">
              <select id="borderType" class="form-control" title="Border style">
                <option value="solid">Solid</option>
                <option value="dashed">Dashed</option>
                <option value="dotted">Dotted</option>
              </select>
            </div>

            <!-- Text Alignment -->
            <div class="editor-toolbar-group" data-group-name="Text Alignment">
              <button id="alignLeftBtn" data-align="left" class="btn btn-light"  title="Align left"><span style="color: black;" class="material-icons">format_align_left</span></button>
              <button id="alignCenterBtn" data-align="center" class="btn btn-light" title="Align center"><span style="color: black;"class="material-icons">format_align_center</span></button>
              <button id="alignRightBtn" data-align="right" class="btn btn-light" title="Align right"><span style="color: black;"class="material-icons">format_align_right</span></button>
              <button id="alignJustifyBtn" data-align="justify" class="btn btn-light" title="Justify"><span style="color: black;" class="material-icons">format_align_justify</span></button>
            </div>

            <!-- Advanced Options -->
            <div class="editor-toolbar-group" data-group-name="Advanced Options">
              <button id="cropImageBtn" class="btn btn-light" title="Crop image"><span style="color: black;" class="material-icons">crop</span></button>
              <button id="addVariableBtn" class="btn btn-light" title="Add variable"><span style="color: black;" class="material-icons">code</span></button>
            </div>
          </div>

          <div class="canvas-editor-main pt-4 pb-2" style="overflow-x: auto; overflow-y: auto; position: relative; display: flex; justify-content: center; align-items: center; width: 100%;">
            <!-- The Fabric.js canvas -->
            <canvas id="canvas-editor" class="border shadow"></canvas>
          </div>
        </div>

        <!-- Height and width define for CanvasArea -->
        <!-- Button to trigger the modal -->



<!-- Sticky Footer starts-->
<div class="sticky-footer sticky-bottom border-top d-flex justify-content-between align-items-center py-3 px-4bg-light">

  <!-- Notes Section -->
  <div class="notes-section position-relative d-flex align-items-center">
    <span id="notesIcon" class="me-2" style="cursor: pointer" title="Add a note">
      <i class="bi bi-pencil"></i> Notes
    </span>
    <div id="notesInputBox" class="position-absolute bottom-100 start-0 mb-2 p-3 border rounded bg-white d-none" style="z-index: 1000;">
      <input type="text" id="noteInput" class="form-control mb-2" placeholder="Enter your note here" />
      <button id="addNoteBtn" class="btn btn-primary btn-sm">Add Note</button>
    </div>
  </div>

  <!-- Zoom and Full-screen controls -->
  <div class="d-flex align-items-center">
    <!-- Zoom Control -->
    <div class="d-flex align-items-center me-3">
      <input type="range" class="form-range me-2" id="zoomRange" min="0.5" max="2" step="0.1" value="1" style="width: 120px;">
      <span id="zoomValue">1.0x</span>
    </div>

    <!-- Full-screen Button -->
    <button id="expandBtn" class="btn btn-outline-secondary btn-sm me-2" title="Fullscreen">
      <i class="fa-solid fa-expand"></i>
    </button>
  </div>

  <!-- Full-screen Preview Container (Initially Hidden) -->
  <div id="fullScreenPreview" class="d-none position-fixed top-0 start-0 w-100 h-100 bg-white">
    <!-- Close Icon -->
    <button id="closeFullScreen" class="position-absolute top-0 end-0 m-3 btn btn-light">
      <i class="fa-solid fa-expand"></i>
    </button>
    <div id="fullScreenContent" class="h-100 justify-content-center align-items-center p-5">
      <!-- Full-screen preview content goes here -->
    </div>
  </div>
</div>
<!-- Sticky Footer ends -->

</div>
<!-- canvas editor section ends -->

  </div>


  <!-- full screen canvas starts -->
<div id="fullScreenPreview" class="d-none">
  <canvas id="fullScreenCanvas"></canvas>
</div>
<!-- full screen canvas ends -->




  <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js" integrity="sha512-CeIsOAsgJnmevfCi2C7Zsyy6bQKi43utIjdA87Q0ZY84oDqnI0uwfM9+bKiIkI75lUeI00WG/+uJzOmuHlesMA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.7.0/dropzone.min.js"></script>
  <script src="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.contextMenu.min.js"></script>
  <script src="https://swisnl.github.io/jQuery-contextMenu/dist/jquery.ui.position.min.js"></script>

  <script>
    $(document).ready(function() {
      // Native color pickers don't need initialization
      console.log('Using native HTML color pickers');

      // Show toolbar when clicking on canvas-editor-main or canvas
      $(".canvas-editor-main, #canvas-editor").on("click", function(event) {
        console.log("Canvas clicked");
        $(".editor-toolbar").removeClass("d-none");
        event.stopPropagation();
      });

      // Keep toolbar visible when clicking on it
      $(".editor-toolbar").on("click", function(event) {
        console.log("Toolbar clicked");
        event.stopPropagation();
      });

      // Toggle dropdown menu when clicking on more options button
      $("#moreOptionsBtn").on("click", function(event) {
        event.preventDefault();
        event.stopPropagation();

        console.log("More options button clicked");
        var dropdown = $("#toolbarDropdown");
        var isVisible = dropdown.hasClass("show");

        // Toggle dropdown visibility
        if(isVisible) {
          // If already visible, hide it
          dropdown.removeClass("show");
          $(this).removeClass("active");
          console.log("Dropdown hidden");
        } else {
          // If hidden, show it
          dropdown.addClass("show");
          $(this).addClass("active");
          console.log("Dropdown shown");

          // Force reposition of dropdown
          positionDropdown();

          // Special handling for mobile
          if(window.innerWidth <= 768) {
            // Scroll to make sure dropdown is visible on mobile
            $('html, body').animate({
              scrollTop: $(document).height() - $(window).height()
            }, 200);
          }
        }

        return false;
      });

      // Also handle touch events for mobile
      $("#moreOptionsBtn").on("touchstart", function(event) {
        $(this).trigger("click");
        event.preventDefault();
      });

      // Hide dropdown when clicking outside
      $(document).on("click", function(event) {
        if(!$(event.target).closest('#toolbarDropdown').length &&
           !$(event.target).closest('#moreOptionsBtn').length) {
          $("#toolbarDropdown").removeClass("show");
          $("#moreOptionsBtn").removeClass("active");
          console.log("Dropdown closed by outside click");
        }
      });

      // Also handle touch events for mobile
      $(document).on("touchstart", function(event) {
        if(!$(event.target).closest('#toolbarDropdown').length &&
           !$(event.target).closest('#moreOptionsBtn').length) {
          $("#toolbarDropdown").removeClass("show");
          $("#moreOptionsBtn").removeClass("active");
          console.log("Dropdown closed by outside touch");
        }
      });

      // Prevent dropdown from closing when clicking inside it
      $("#toolbarDropdown").on("click touchstart", function(event) {
        event.stopPropagation();
        console.log("Click inside dropdown");
      });

      // Add a tap overlay for mobile to ensure we can close the dropdown
      $("<div id='mobile-tap-overlay' class='d-none'></div>").appendTo("body");

      // Make sure dropdown is properly positioned relative to the toolbar
      function positionDropdown() {
        const toolbar = $(".editor-toolbar");
        const dropdown = $("#toolbarDropdown");
        if(toolbar.length && dropdown.length) {
          // Different positioning for mobile vs desktop
          if(window.innerWidth <= 768) {
            // On mobile, position above the toolbar
            dropdown.css({
              'position': 'fixed',
              'top': 'auto',
              'bottom': (toolbar.outerHeight() + 10) + 'px',
              'left': '50%',
              'transform': 'translateX(-50%)',
              'width': '90%'
            });
          } else {
            // On desktop, position below the toolbar
            const toolbarHeight = toolbar.outerHeight();
            dropdown.css({
              'position': 'absolute',
              'top': (toolbar.position().top + toolbarHeight + 5) + 'px',
              'bottom': 'auto',
              'left': '50%',
              'transform': 'translateX(-50%)',
              'width': '95%'
            });
          }
        }
      }

      // Position dropdown when toolbar becomes visible
      $(document).on('click', '.canvas-editor-main', function() {
        setTimeout(positionDropdown, 100);
      });

      // Hide toolbar when clicking outside canvas-editor-main and editor-toolbar
      $(document).on("click", function(event) {
        console.log("Document clicked");
        if (!$(event.target).closest(".canvas-editor-main, .editor-toolbar, #canvas-editor").length) {
          $(".editor-toolbar").addClass("d-none");
        }
      });

      // Background color picker handler
      $('#bgColorPicker').on('input change', function() {
        let color = $(this).val();
        if (color && typeof canvas !== 'undefined') {
          canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas));
        }
      });

      // Element fill color picker handler
      $('#elementFillPicker').on('input change', function() {
        let color = $(this).val();
        if (typeof canvas !== 'undefined') {
          const activeObject = canvas.getActiveObject();
          if (activeObject) {
            activeObject.set({ fill: color });
            canvas.renderAll();
          }
          // Don't show any alert if no object is selected
        }
      });

      // Font color picker handler
      $('#fontColorPicker').on('input change', function() {
        let color = $(this).val();
        if (typeof canvas !== 'undefined') {
          const activeObject = canvas.getActiveObject();
          if (activeObject && activeObject.type === 'textbox') {
            activeObject.set('fill', color);
            canvas.renderAll();
          }
          // Don't show any alert if no object is selected
        }
      });

      // Border color picker handler
      $('#borderColorPicker').on('input change', function() {
        let color = $(this).val();
        if (typeof canvas !== 'undefined') {
          const activeObject = canvas.getActiveObject();
          if (activeObject) {
            activeObject.set('stroke', color);
            canvas.renderAll();
          }
          // Don't show any alert if no object is selected
        }
      });

      // Log to console that color pickers are initialized
      console.log('Native HTML color pickers initialized');
    });
  </script>


  <script>
    $(document).ready(function(){
        $('#v-pills-tab button').on('click', function() {
            // Remove 'active' class from all buttons
            $('#v-pills-tab button').removeClass('active');

            // Add 'active' class to the clicked button
            $(this).addClass('active');
            $('#v-pills-tabContent').removeClass('d-none');

            const data = $(this).attr('data-bs-target');
            console.log("Activating tab:", data); // Debugging log

            // Hide all other content sections
            $('.tab-pane').removeClass('show active');


            // Show the selected tab content
            $(data).addClass('show active');
            $("#closeSidebarBtn").removeClass("d-none");
        });
    });
</script>


  <!-- Upload and pull images script starts -->
  <script>
    // Initialize Dropzone
  Dropzone.autoDiscover = false;

  const myDropzone = new Dropzone("#imageDropzone", {
    paramName: "file", // The name that will be used to transfer the file
    maxFilesize: 25, // MB
    acceptedFiles: "image/*,.svg,.ai",
    dictDefaultMessage: "Drag & drop files here or click to upload",
    success: function(file, response) {
      // After a successful upload, fetch and display all images
      fetchUploadedImages();
    },
    error: function(file, response) {
      // Handle upload error
      console.error('Upload failed:', response);
    }
  });





  function fetchUploadedImages() {
      $.ajax({
          url: '/api/files/images',
          method: 'GET',
          success: function(data) {
              const $imageContainer = $('#uploadedImages');
              $imageContainer.empty(); // Clear existing images

              data.forEach(file => {
                  // Create image wrapper
                  const $imgWrapper = $('<div>', { class: 'image-wrapper', style: 'position: relative; display: inline-block; margin: 5px;' });

                  // Create image element
                  const $imgElement = $('<img>', {
                      class: "my-single-image",
                      src: file.url,
                      alt: file.fileName,
                      style: 'max-width: 150px;'
                  });

                  // Create download button
                  const $downloadBtn = $('<button>', {
                      class: 'btn btn-sm btn-secondary upload-image-btn',
                      html: '<i class="fas fa-download"></i>',
                      style: 'position: absolute; top: 10px; right: 40px; display: none;'
                  }).on('click', function() {

                    const $a = $('<a>', {
                          href: file.url,
                          download: file.fileName
                      });
                      $a[0].click(); // Programmatically trigger the download
                                      });

                  // Create delete button
                  const $deleteBtn = $('<button>', {
                      class: 'btn btn-sm btn-secondary upload-image-btn',
                      html: '<i class="fas fa-trash-alt"></i>',
                      style: 'position: absolute; top: 10px; right: 10px; display: none;'
                  }).on('click', function() {
                      if (confirm('Are you sure you want to delete this file?')) {
                          $.ajax({
                              url: `/api/files/${file._id}`,
                              method: 'DELETE',
                              success: function() {
                                  fetchUploadedImages(); // Refresh image list after delete
                              },
                              error: function(err) {
                                  console.error('Error deleting image:', err);
                              }
                          });
                      }
                  });

                  // Append image and buttons to wrapper
                  $imgWrapper.append($imgElement, $downloadBtn, $deleteBtn);
                  $imageContainer.append($imgWrapper);

                  // Show buttons on hover
                  $imgWrapper.hover(
                      function() { $downloadBtn.show(); $deleteBtn.show(); },
                      function() { $downloadBtn.hide(); $deleteBtn.hide(); }
                  );




              });


            // Add to canvas on click starts
            $(".my-single-image").on('click', function () {
                // Get the src of the clicked image
                var imgSrc = $(this).attr('src');  // Use attr to get the image source

                // Create an image element to load the image
                var imgElement = new Image();
                imgElement.crossOrigin = 'anonymous';  // Handle CORS for cross-origin images
                imgElement.src = imgSrc;

                // When the image is loaded
                imgElement.onload = function () {
                    // Create a temporary canvas to convert the image to base64
                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');

                    // Set the canvas size to the image size
                    tempCanvas.width = imgElement.width;
                    tempCanvas.height = imgElement.height;

                    // Draw the image on the temporary canvas
                    tempCtx.drawImage(imgElement, 0, 0);

                    // Convert to base64
                    var base64Image = tempCanvas.toDataURL('image/png');

                    // Add image to Fabric.js canvas using the base64 data
                    fabric.Image.fromURL(base64Image, function (img) {
                        img.scaleToWidth(150);  // Adjust size if necessary
                        img.scaleToHeight(150);

                        // Position the image at the center of the canvas
                        img.set({
                            left: canvas.width / 2 - img.getScaledWidth() / 2,  // Center horizontally
                            top: canvas.height / 2 - img.getScaledHeight() / 2  // Center vertically
                        });

                        // Add the image to the canvas and render
                        canvas.add(img);
                        canvas.renderAll();  // Render the canvas to show the image

                        // Optionally log the base64 string if needed
                        console.log(base64Image);  // This will log the base64 string to the console
                    });
                };
            });
            // Add to canvas on click ends


          },
          error: function(err) {
              console.error('Error fetching images:', err);
          }
      });
  }

  // Call the function on page load
  $(document).ready(function() {
      fetchUploadedImages();
  });



  </script>
  <!-- Upload and pull images script ends -->


  <!-- handle full screen, zoom range, notes start -->
   <script>
    $(document).ready(function () {
  // Notes Section Toggle
  $("#notesIcon").click(function () {
    var $notesBox = $("#notesInputBox");
    if ($notesBox.hasClass("d-none")) {
      $notesBox.removeClass("d-none");
    } else {
      $notesBox.addClass("d-none");
    }
  });

  // Add Note Functionality (Extend this as per your need)
  $("#addNoteBtn").click(function () {
    const noteText = $("#noteInput").val();
    if (noteText.trim() !== "") {
      alert("Note added: " + noteText); // You can replace this with a logic to save the note
      $("#noteInput").val(""); // Clear the input field after adding the note
      $("#notesInputBox").addClass("d-none");
    } else {
      alert("Please enter a note.");
    }
  });

  // Zoom Range Slider (Works with canvas-editor id)
  $("#zoomRange").on("input", function () {
    var zoomLevel = parseFloat($(this).val());
    $("#zoomValue").text(zoomLevel + "x");

    // Get the current base scale from the container size
    const parent = document.querySelector(".canvas-editor-main");
    if (!parent) return;

    const containerWidth = parent.clientWidth - 40;
    const containerHeight = parent.clientHeight - 40;
    const scaleX = containerWidth / originalWidth;
    const scaleY = containerHeight / originalHeight;
    const baseScale = Math.min(scaleX, scaleY); // Match the scale calculation in resizeCanvas

    // Apply the user's zoom on top of the base scale
    const newZoom = baseScale * zoomLevel;

    // Apply the zoom
    canvas.setZoom(newZoom);

    // Adjust canvas dimensions
    canvas.setWidth(originalWidth * newZoom);
    canvas.setHeight(originalHeight * newZoom);

    // Center the canvas in the container
    const leftOffset = (containerWidth - (originalWidth * newZoom)) / 2;
    const topOffset = (containerHeight - (originalHeight * newZoom)) / 2;

    // Check if sidebar is collapsed and apply different positioning
    if ($('body').hasClass('sidebar-collapsed')) {
        // Position the canvas wrapper absolutely in the center when sidebar is collapsed
        canvas.wrapperEl.style.position = 'absolute';
        canvas.wrapperEl.style.left = '50%';
        canvas.wrapperEl.style.top = '50%';
        canvas.wrapperEl.style.transform = 'translate(-50%, -50%)';
    } else {
        // Center the canvas in the available space
        canvas.wrapperEl.style.position = 'absolute';
        canvas.wrapperEl.style.left = '50%';
        canvas.wrapperEl.style.top = '50%';
        canvas.wrapperEl.style.transform = 'translate(-50%, -50%)';
        canvas.wrapperEl.style.margin = '';
    }

    // Render the canvas with the new settings
    canvas.renderAll();
});

  // Full-screen Toggle
$("#expandBtn").click(function () {
    var elem = $("#fullScreenPreview")[0];

    // Full-screen canvas dimensions (fill the screen)
    var fullScreenWidth = window.innerWidth;
    var fullScreenHeight = window.innerHeight;

    // Create a new canvas instance for the full-screen canvas
    var fullScreenCanvas = new fabric.Canvas('fullScreenCanvas', {
        width: fullScreenWidth,
        height: fullScreenHeight,
        backgroundColor: 'white' // Same as original
    });

    // Copy the canvas objects from the original editor canvas to the full-screen canvas
    var canvasData = canvas.toJSON();

    // Adjust scaling based on original canvas dimensions to full-screen canvas dimensions
    var scaleX = fullScreenWidth / canvas.width;
    var scaleY = fullScreenHeight / canvas.height;

    // Load the content and scale objects
    fullScreenCanvas.loadFromJSON(canvasData, function () {
        // Scale all objects on the full-screen canvas
        fullScreenCanvas.forEachObject(function (obj) {
            obj.scaleX *= scaleX;
            obj.scaleY *= scaleY;
            obj.left *= scaleX;
            obj.top *= scaleY;
            obj.setCoords(); // Adjust object coordinates
        });

        // Re-render full-screen canvas after scaling
        fullScreenCanvas.renderAll();
    });

    // Show the full-screen preview
    $("#fullScreenPreview").removeClass("d-none").addClass("d-flex");

    // Enter full-screen mode for the preview container
    if (elem.requestFullscreen) {
        elem.requestFullscreen().catch(function (error) {
            console.error("Failed to enter full-screen mode:", error);
        });
    } else if (elem.webkitRequestFullscreen) {
        elem.webkitRequestFullscreen().catch(function (error) {
            console.error("Failed to enter full-screen mode:", error);
        });
    } else if (elem.mozRequestFullScreen) {
        elem.mozRequestFullScreen().catch(function (error) {
            console.error("Failed to enter full-screen mode:", error);
        });
    } else if (elem.msRequestFullscreen) {
        elem.msRequestFullscreen().catch(function (error) {
            console.error("Failed to enter full-screen mode:", error);
        });
    }
});


  // Close Full-screen
  $("#closeFullScreen").click(function () {
    if (document.exitFullscreen) {
      document.exitFullscreen().catch(function (error) {
        console.error("Failed to exit full-screen mode:", error);
      });
    } else if (document.webkitExitFullscreen) {
      // Safari
      document.webkitExitFullscreen().catch(function (error) {
        console.error("Failed to exit full-screen mode:", error);
      });
    } else if (document.mozCancelFullScreen) {
      // Firefox
      document.mozCancelFullScreen().catch(function (error) {
        console.error("Failed to exit full-screen mode:", error);
      });
    } else if (document.msExitFullscreen) {
      // IE/Edge
      document.msExitFullscreen().catch(function (error) {
        console.error("Failed to exit full-screen mode:", error);
      });
    }

    $("#fullScreenPreview").removeClass("d-flex").addClass("d-none");
  });

  // Exit full-screen if user presses the Escape key
  $(document).on("keydown", function (event) {
    if (event.key === "Escape" && document.fullscreenElement) {
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(function (error) {
          console.error("Failed to exit full-screen mode:", error);
        });
      } else if (document.webkitExitFullscreen) {
        // Safari
        document.webkitExitFullscreen().catch(function (error) {
          console.error("Failed to exit full-screen mode:", error);
        });
      } else if (document.mozCancelFullScreen) {
        // Firefox
        document.mozCancelFullScreen().catch(function (error) {
          console.error("Failed to exit full-screen mode:", error);
        });
      } else if (document.msExitFullscreen) {
        // IE/Edge
        document.msExitFullscreen().catch(function (error) {
          console.error("Failed to exit full-screen mode:", error);
        });
      }

      $("#fullScreenPreview").removeClass("d-flex").addClass("d-none");
    }
  });
});

   </script>
  <!-- handle full screen, zoom range, notes ends -->



    <!-- create or update a new certificate starts -->
    <script>
      // Initialize the Fabric.js canvas with default dimensions
      // var canvas = new fabric.Canvas('canvas-editor', {
      //     width: width,  // initial width
      //     height: height, // initial height
      //     backgroundColor: 'white'
      // });

      // Function to fetch design data if design ID exists in the URL
      function loadDesignIfExists() {
    var currentUrl = window.location.href;
    var designIdMatch = currentUrl.match(/\/design\/(\w+)/);
    var designId = designIdMatch ? designIdMatch[1] : null;

    if (designId) {
        $.ajax({
            url: `/api/design/${designId}`,
            type: 'GET',
            success: function(response) {
                console.log('Fetched design data:', response);

                // Load the canvas dimensions if available
                if (response.canvas) {
                    var canvasData = response.canvas;

                    // Check for width and height in the canvas data
                    if (canvasData.width && canvasData.height) {
                        canvas.setWidth(canvasData.width);
                        canvas.setHeight(canvasData.height);
                        console.log(`Canvas resized to: ${canvasData.width} x ${canvasData.height}`);
                    }

                    // Load the canvas objects and render
                    canvas.loadFromJSON(canvasData, canvas.renderAll.bind(canvas));

                    //set the name to

                    $("#designName").val(response.designName)

                    console.log('Canvas data loaded and rendered.');


                } else {
                    console.error('No canvas data found in the response.');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error fetching design data:', status, error);
            }
        });
    }
}

      // Call this function to check for designId and load data
      loadDesignIfExists();

      // Function to save or update the canvas data
  // Function to save or update the canvas data
function saveCanvas() {
    //console.log("Current canvas", canvas);
    //console.log("Current canvas JSON", canvas.toJSON());

    var $saveButton = $('#saveCanvas');

        // Add animation to the button
        $saveButton.prop('disabled', true); // Disable the button
        $saveButton.html(`
  <span class="d-flex align-items-center">
    <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
    Saving
  </span>
`);


    // Extract the design ID from the current URL
    var currentUrl = window.location.href;
    var designIdMatch = currentUrl.match(/\/design\/(\w+)/); // Regex to extract design ID if it's in the URL
    var designId = designIdMatch ? designIdMatch[1] : null; // Extract design ID or null if not found

    // Add width and height to the canvas data before saving
    var canvasData = canvas.toJSON();
    canvasData.width = canvas.getWidth();
    canvasData.height = canvas.getHeight();

    // Upload the canvas image first to get the URL
    uploadCanvasImage()
        .then((imageUrl) => {
            console.log('Image uploaded successfully at:', imageUrl);

            // Prepare the data object for both POST and PUT/PATCH requests
            var requestData = JSON.stringify({
                designName: $("#designName").val(),
                imageURL: imageUrl,  // Use the uploaded image URL here
                canvas: canvasData  // Save the canvas JSON including width and height
            });

            // If designId exists, update; otherwise, create new template
            if (designId) {
                $.ajax({
                    url: `/api/design/${designId}`,
                    type: 'PUT',
                    contentType: 'application/json',
                    data: requestData,
                    success: function(response) {
                        console.log('Certificate Design saved successfully:', response);
                        // Revert the button back to its original state after saving is complete
                        $saveButton.text('Saved');
                        $saveButton.prop('disabled', false); // Enable the button again
                        setTimeout(function() {
                            $saveButton.html(`<i class="fas fa-save me-2"></i>
                            Save
                            `);
                        }, 4000);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error updating certificate template:', status, error);
                    }
                });
            } else {
                $.ajax({
                    url: '/api/design/',
                    type: 'POST',
                    contentType: 'application/json',
                    data: requestData,
                    success: function(response) {
                        console.log('Certificate template created successfully:', response);
                        var newTemplateId = response._id;
                        var newUrl = `${window.location.origin}/design/${newTemplateId}`;
                        window.history.pushState({ path: newUrl }, '', newUrl);
                        console.log('Updated URL:', newUrl);
                        // Revert the button back to its original state after saving is complete
                        $saveButton.text('Saved');
                        $saveButton.prop('disabled', false); // Enable the button again
                        setTimeout(function() {
                            $saveButton.html(`<i class="fas fa-save me-2"></i>
                            Save
                            `);
                        }, 4000);

                    },
                    error: function(xhr, status, error) {
                        console.error('Error creating certificate template:', status, error);
                    }
                });
            }
        })
        .catch((error) => {
            console.error('Error uploading image:', error);
        });
}

      // Call this function to save the canvas
      document.getElementById("saveCanvas").addEventListener("click", saveCanvas);
  </script>

<!-- create or update a new certificate ends -->


  <!-- handle create new button actions starts-->
<script>

  function openEditor(type) {
      let width, height;
      switch (type) {
          case 'horizontal':
              width = 800; height = 600;
              break;
          case 'vertical':
              width = 600; height = 800;
              break;
          case 'badge':
              width = 300; height = 300;
              break;
      }
      window.open(`/design/?width=${width}&height=${height}`, '_blank');
  }

  function createCustomDesign() {
      const width = document.getElementById('customWidth').value;
      const height = document.getElementById('customHeight').value;
      if (width && height) {
          window.open(`/design/?width=${width}&height=${height}`, '_blank');
      } else {
          alert('Please enter both width and height.');
      }
  }
  </script>
  <!-- handle create new button actions ends -->


    <!-- canvas script starts -->
    <script>

// Function to extract query parameters from the URL
function getQueryParams() {
    const params = new URLSearchParams(window.location.search);
    const width = parseInt(params.get('width')) || 800; 
    const height = parseInt(params.get('height')) || 600; 
    const name = params.get('name');
    return { width, height, name };
}

// Get canvas dimensions and design name from the URL
const { width, height, name } = getQueryParams();

// Store original canvas dimensions
const originalWidth = width;
const originalHeight = height;

// Initialize the Fabric.js canvas with original dimensions
var canvas = new fabric.Canvas('canvas-editor', {
    width: originalWidth,
    height: originalHeight,
    backgroundColor: 'white'
});

// Function to resize the canvas dynamically with proper scaling
function resizeCanvas() {
    const parent = document.querySelector(".canvas-editor-main");
    if (!parent) return;

    // Get available space in the container
    const containerWidth = parent.clientWidth - 40; // Subtract padding
    const containerHeight = parent.clientHeight - 40; // Subtract padding

    // Calculate the scale to fit the canvas in the container while maintaining aspect ratio
    const scaleX = containerWidth / originalWidth;
    const scaleY = containerHeight / originalHeight;
    const scale = Math.min(scaleX, scaleY); // Use the smaller scale to ensure it fits

    // Apply the zoom level to the canvas
    canvas.setZoom(scale);

    // Keep the original dimensions of the canvas
    canvas.setWidth(originalWidth * scale);
    canvas.setHeight(originalHeight * scale);

    // Center the canvas in the container
    const leftOffset = (containerWidth - (originalWidth * scale)) / 2;
    const topOffset = (containerHeight - (originalHeight * scale)) / 2;

    // Check if sidebar is collapsed and apply different positioning
    if ($('body').hasClass('sidebar-collapsed')) {
        // When sidebar is collapsed, center the canvas container
        $('.canvas-editor-main').css({
            'display': 'flex',
            'justify-content': 'center',
            'align-items': 'center'
        });

        // Center the canvas column
        $('.col-sm-12.col-md-5.col-lg-8').css({
            'display': 'flex',
            'justify-content': 'center',
            'align-items': 'center',
            'width': '100%',
            'max-width': '100%',
            'margin': '0 auto'
        });

        // Position the canvas wrapper absolutely in the center
        canvas.wrapperEl.style.position = 'absolute';
        canvas.wrapperEl.style.left = '50%';
        canvas.wrapperEl.style.top = '50%';
        canvas.wrapperEl.style.transform = 'translate(-50%, -50%)';
    } else {
        // When sidebar is open, use the original positioning
        $('.canvas-editor-main').css({
            'display': 'flex',
            'justify-content': 'center',
            'align-items': 'center'
        });

        // Reset canvas column styles
        $('.col-sm-12.col-md-5.col-lg-8').css({
            'display': 'flex',
            'justify-content': 'center',
            'align-items': 'center',
            'width': '',
            'max-width': '',
            'margin-left': ''
        });

        // Center the canvas in the available space
        canvas.wrapperEl.style.position = 'absolute';
        canvas.wrapperEl.style.left = '50%';
        canvas.wrapperEl.style.top = '50%';
        canvas.wrapperEl.style.transform = 'translate(-50%, -50%)';
        canvas.wrapperEl.style.margin = '';
    }

    // Render the canvas with the new settings
    canvas.renderAll();
}

// Add a debounce function to prevent excessive resizing
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// Add event listener for window resize with debounce
window.addEventListener('resize', debounce(function() {
    resizeCanvas();
}, 250));

// Initial resize
resizeCanvas();
//customizing the fabric to store data variable
fabric.Textbox.prototype.toObject = (function(toObject) {
    return function() {
        return fabric.util.object.extend(toObject.call(this), {
            data: this.data || {}
        });
    };
})(fabric.Textbox.prototype.toObject);



// Function to parse and limit the design name to a maximum of 30 characters
function parseString(str) {
    if (!str) return "Untitled Design Name"; // Return default name if no 'name' is provided
    return str.length > 30 ? str.substring(0, 30) : str; // Truncate if string is longer than 30 chars
}

// Append resolution (height x width) to the design name
function appendResolution(name, height, width) {
    return `${name} (${height}x${width})`; // Append height and width to the name
}

// Set the design name with a maximum of 30 characters and add resolution at the end
const truncatedName = parseString(name) || "Untitled Design Name"; // Get the truncated design name
const finalNameWithResolution = appendResolution(truncatedName, 1620, 1120); // Add resolution

// Set the design name in the input field
$("#designName").val(finalNameWithResolution); // Update the input field



// if(width && height && name){
// console.log(
//   `🎨 Canvas initialized:\n` +
//   `- Dimensions: ${width}x${height} (Width x Height)\n` +
//   `- Design Name: ${name}\n` +
//   `Triggered when a new design is selected from the design list.`
// );
// }






  //COPY VARIABLES
var copiedObject = null;
var copiedObjectStyle = null;

  // Add a static background to the canvas
  var backgroundRect = new fabric.Rect({
      left: 120,
      top: 0,
      width: 800,
      height: 600,
      fill: '#f4f4f4',
      selectable: false // Prevent user from selecting or editing this element
  });


  // Add a fixed logo image
  fabric.Image.fromURL('/uploads/1727203930884-MixCertificate.png', function(img) {
      img.set({
          left: 50,
          top: 350,
          scaleX: 0.5,
          scaleY: 0.5,
          selectable: true
      });
      canvas.add(img);
  });
  // Add a fixed logo image
  fabric.Image.fromURL('/uploads/1727203930884-MixCertificate.png', function(img) {
      img.set({
          left: width/2,
          top: height/2,
          scaleX: 0.5,
          scaleY: 0.5,
          selectable: true
      });
      //canvas.add(img);
  });

  // Add a title to the template
  var titleText = new fabric.Text('Certificate of Achievement', {
      left: 100,
      top: 50,
      fontSize: 36,
      fontFamily: 'Arial',
      fill: '#333',
      selectable: true,
  });
  //canvas.add(titleText);





  //right click menu starts

        // Right-click context menu setup
        $.contextMenu({
            selector: 'canvas', // target the canvas
            callback: function (key, options) {
                var activeObject = canvas.getActiveObject();
                if (!activeObject) return;

                switch (key) {
                    case 'copy':
                        copiedObject = fabric.util.object.clone(activeObject);
                        break;
                    case 'copyStyle':
                        copiedObjectStyle = {
                            fill: activeObject.fill,
                            stroke: activeObject.stroke,
                            strokeWidth: activeObject.strokeWidth
                        };
                        break;
                    case 'paste':
                        if (copiedObject) {
                            copiedObject.clone(function (cloned) {
                                cloned.set({
                                    left: cloned.left + 20,
                                    top: cloned.top + 20,
                                    evented: true
                                });
                                canvas.add(cloned);
                                canvas.setActiveObject(cloned);
                                canvas.renderAll();
                            });
                        }
                        break;
                    case 'duplicate':
                        activeObject.clone(function (cloned) {
                            cloned.set({
                                left: cloned.left + 20,
                                top: cloned.top + 20
                            });
                            canvas.add(cloned);
                            canvas.setActiveObject(cloned);
                            canvas.renderAll();
                        });
                        break;
                    case 'delete':
                        canvas.remove(activeObject);
                        break;
                    case 'lock':
                        activeObject.set({ selectable: false });
                        break;
                    case 'unlock':
                        activeObject.set({ selectable: true });
                        break;
                    case 'alignToPage':
                        activeObject.set({
                            left: canvas.width / 2 - activeObject.width / 2,
                            top: canvas.height / 2 - activeObject.height / 2
                        });
                        canvas.renderAll();
                        break;
                    case 'setAsBackground':
                        canvas.setBackgroundImage(activeObject.toDataURL(), canvas.renderAll.bind(canvas));
                        break;
                    case 'bringForward':
                        canvas.bringForward(activeObject);
                        canvas.renderAll();
                        break;
                    case 'sendBackwards':
                        canvas.sendBackwards(activeObject);
                        canvas.renderAll();
                        break;
                    case 'link':
                        // Example functionality for linking (can be extended)
                        var link = prompt("Enter a link for the object:");
                        if (link) {
                            activeObject.link = link;
                        }
                        break;
                }
            },
            items: {
                "copy": { name: "Copy" },
                "copyStyle": { name: "Copy Style" },
                "paste": { name: "Paste" },
                "duplicate": { name: "Duplicate" },
                "delete": { name: "Delete" },
                "sep1": "---------",
                "bringForward": { name: "Bring Forward" },
                "sendBackwards": { name: "Send Backwards" },
                "lock": { name: "Lock" },
                "unlock": { name: "Unlock" },
                "alignToPage": { name: "Align to Page" },
                "link": { name: "Add Link" },
                "setAsBackground": { name: "Set Image as Background" }
            }
        });

        // Tooltip for Notes (you can customize it further)
        var noteTooltip = $('<div>', {
            id: 'noteTooltip',
            class: 'd-none bg-light p-2 border rounded shadow',
            text: 'Add a note'
        }).appendTo('body');

        $(document).on('mousemove', function (e) {
            noteTooltip.css({
                left: e.pageX + 10 + 'px',
                top: e.pageY + 10 + 'px'
            });
        });

        canvas.on('mouse:down', function (opt) {
            var evt = opt.e;
            if (evt.button === 3) { // Right-click
                evt.preventDefault();
                var pointer = canvas.getPointer(evt);
                canvas.setActiveObject(canvas.findTarget(pointer));
                canvas.renderAll();
            }
        });

  //right click menu ends

  </script>
  <!-- canvas script ends -->

  <!-- pixabay search starts -->
  <script>
    var page = 1; // Current page number
    var query = ''; // Current search query
    var isLoading = false; // To prevent multiple simultaneous requests

    // Search Pixabay function
    function searchPixabay(query, page) {
        if (isLoading) return;
        isLoading = true;

        $('#loading').removeClass('d-none'); // Show loading spinner

        $.ajax({
            url: `/api/pixabay?q=${query}&type=photo&per_page=20&hits=18&page=${page}`,
            method: 'GET',
            success: function (data) {
                displayResults(data.hits);
                isLoading = false;
                $('#loading').addClass('d-none'); // Hide loading spinner
            },
            error: function (err) {
                console.error('Error fetching Pixabay images:', err);
                isLoading = false;
                $('#loading').addClass('d-none'); // Hide loading spinner
            }
        });
    }

    // Function to display search results
    function displayResults(images) {
        var resultsContainer = $('#searchResults');

        images.forEach(function (image) {
            var imgElement = $('<img>')
                .attr('src', image.previewURL)
                .attr('class', 'image-item img-thumbnail')
                .attr('data-full', image.largeImageURL)
                .attr('alt', 'Pixabay Image')
                .attr('crossorigin', 'anonymous');

            // Attach click event for dragging to Fabric.js canvas
            imgElement.on('click', function () {
                addImageToCanvas($(this).data('full'));
            });

            resultsContainer.append(imgElement);
        });
    }

    // Function to add image to Fabric.js canvas
    function addImageToCanvas(imageUrl) {
        fabric.Image.fromURL(imageUrl, function (img) {
            img.scaleToWidth(150);  // Set image width
            img.scaleToHeight(150); // Set image height
            canvas.add(img);
            canvas.renderAll();
        },{ crossOrigin: 'anonymous' });
    }

    // Search button click event
    $('#searchBtn').on('click', function () {
        query = $('#searchQuery').val().trim(); // Get the current search query value
        page = 1; // Reset to first page on new search
        $('#searchResults').empty(); // Clear previous results
        if (query) {
            searchPixabay(query, page); // Trigger search
        } else {
            alert('Please enter a search term');
        }
    });

    // Infinite scroll event
    $(window).on('scroll', function () {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 50) {
            // Load more images if scrolled near the bottom
            if (!isLoading && query) {
                page++; // Increment page number
                searchPixabay(query, page); // Fetch more results
            }
        }
    });

    // Initialize by using the current value in the search box, if present
    $(document).ready(function () {
        if ($('#searchQuery').val().trim()) {
            query = $('#searchQuery').val().trim();
            searchPixabay(query, page); // Perform search on page load if input has a value
        }
    });
</script>
  <!-- pixabay search ends -->


  <!-- fontfinder starts -->
  <script>
    var iconPage = 1; // Current page number for icons
    var iconQuery = ''; // Current search query for icons
    var iconLoading = false; // To prevent multiple simultaneous requests

    // Search Iconfinder function
    function searchIconfinder(query, page) {
        if (iconLoading) return;
        iconLoading = true;

        $('#iconLoading').removeClass('d-none'); // Show loading spinner

        $.ajax({
            url: `/api/iconfinder?query=${query}&limit=60&page=${page}`,
            method: 'GET',
            success: function (data) {
                displayIconResults(data.icons);
                iconLoading = false;
                $('#iconLoading').addClass('d-none'); // Hide loading spinner
            },
            error: function (err) {
                console.error('Error fetching Iconfinder icons:', err);
                iconLoading = false;
                $('#iconLoading').addClass('d-none'); // Hide loading spinner
            }
        });
    }

    // Function to display icon search results
    function displayIconResults(icons) {
        var resultsContainer = $('#iconResults');

        icons.forEach(function (icon) {
            var previewUrl = getBestPreviewUrl(icon.raster_sizes);
            var downloadUrl = getBestDownloadUrl(icon.raster_sizes); // Use highest resolution for dragging to canvas

            if (previewUrl && downloadUrl) {
                var imgElement = $('<img>')
                    .attr('src', previewUrl)
                    .attr('class', 'icon-item img-thumbnail m-2')
                    .attr('data-full', downloadUrl) // Use high-res download URL for the canvas
                    .attr('alt', 'Iconfinder Icon')
                    .css({ 'width': '50px', 'height': '50px' }); // Set icon size for display

                resultsContainer.append(imgElement);
            }
        });
    }

    // Function to get the best preview URL (smaller image for preview)
    function getBestPreviewUrl(rasterSizes) {
        var bestSize = rasterSizes.find(size => size.size === 64); // Prefer 64x64 for preview
        if (!bestSize) {
            bestSize = rasterSizes[rasterSizes.length - 1]; // Fallback to largest available size
        }
        if (bestSize && bestSize.formats && bestSize.formats.length > 0) {
            return bestSize.formats[0].preview_url; // Return preview URL
        }
        return null;
    }

    // Function to get the best download URL (larger image for canvas)
    function getBestDownloadUrl(rasterSizes) {
        var bestSize = rasterSizes.find(size => size.size >= 512); // Prefer 512x512 or larger for high quality
        if (!bestSize) {
            bestSize = rasterSizes[rasterSizes.length - 1]; // Fallback to largest available size
        }
        if (bestSize && bestSize.formats && bestSize.formats.length > 0) {
            return bestSize.formats[0].download_url; // Return high-res download URL
        }
        return null;
    }

    // Function to add icon to Fabric.js canvas using high-res image
    function addIconToCanvas(iconUrl) {
      console.log("iconUrl: ", iconUrl)

        fabric.Image.fromURL(iconUrl, function (img) {

            img.scaleToWidth(100);  // Set desired width for the canvas
            img.scaleToHeight(100); // Set desired height for the canvas
            canvas.add(img);
            canvas.renderAll();
        }, { crossOrigin: 'anonymous' }); // Enable cross-origin image loading
    }

    // Event delegation for dynamically added image elements
    $(document).on('click', '.icon-item', function () {
        //var fullUrl = $(this).attr('data-full'); // Retrieve high-res image URL
        var fullUrl = $(this).attr('src'); // Retrieve high-res image URL
        addIconToCanvas(fullUrl); // Call the function to add image to canvas
    });

    // Search button click event
    $('#searchIconBtn').on('click', function () {
        iconQuery = $('#searchIconQuery').val().trim(); // Get the current search query value
        iconPage = 1; // Reset to first page on new search
        $('#iconResults').empty(); // Clear previous results
        if (iconQuery) {
            searchIconfinder(iconQuery, iconPage); // Trigger search
        } else {
            alert('Please enter a search term');
        }
    });

    // Infinite scroll event
    $(window).on('scroll', function () {
        if ($(window).scrollTop() + $(window).height() >= $(document).height() - 50) {
            // Load more icons if scrolled near the bottom
            if (!iconLoading && iconQuery) {
                iconPage++; // Increment page number
                searchIconfinder(iconQuery, iconPage); // Fetch more results
            }
        }
    });

    // Initialize by using the current value in the search box, if present
    $(document).ready(function () {
        if ($('#searchIconQuery').val().trim()) {
            iconQuery = $('#searchIconQuery').val().trim();
            searchIconfinder(iconQuery, iconPage); // Perform search on page load if input has a value
        }
    });
</script>

  <!-- fontfinder ends -->



  <!-- QR CODE STARTS -->
  <script>
    $(document).ready(function () {

        $('#generateQrBtn').on('click', function () {
            var url = $('#website').val().trim();

            if (!url) {
                alert('Please enter a URL');
                return;
            }

            $.ajax({
                url: '/api/qr/basic',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ url: url }),
                success: function (response) {
                    if (response.qrCodeUrl) {
                        // Clear previous QR code
                        $('#qrCodeContainer').empty();

                        // Create and append new QR code image
                        var qrImg = $('<img>')
                            .attr('src', response.qrCodeUrl)
                            .attr('alt', 'QR Code')
                            .addClass('img-fluid')
                            .css('cursor', 'pointer');

                        // Add click event to add QR code to Fabric.js canvas
                        qrImg.on('click', function () {
                            fabric.Image.fromURL(response.qrCodeUrl, function (img) {
                                img.scaleToWidth(150);  // Adjust size if necessary
                                img.scaleToHeight(150);

                                // Calculate center position of the canvas
                                var canvasCenterX = canvas.getWidth() / 2;
                                var canvasCenterY = canvas.getHeight() / 2;

                                // Set the image position to the center of the canvas
                                img.set({
                                    left: canvasCenterX,
                                    top: canvasCenterY,
                                    originX: 'center',
                                    originY: 'center'
                                });

                                // Add the image to the canvas
                                canvas.add(img);
                                canvas.renderAll();  // Render the canvas to show the image
                            });
                        });

                        $('#qrCodeContainer').append(qrImg);
                    } else {
                        alert('Error generating QR Code');
                    }
                },
                error: function (error) {
                    console.error('Error:', error);
                    alert('An error occurred while generating the QR Code');
                }
            });
        });
    });
    </script>
  <!-- QR CODE ENDS -->


  <!-- collpse content sidebar starts -->
  <script>
    $(document).ready(function() {
      // Event listener for the button click
      $('#closeSidebarBtn').click(function() {
        // Toggle the material icon
        var $icon = $(this).find('.material-icons');
        if ($icon.text() === 'chevron_left') {
          $icon.text('chevron_right');
        } else {
          $icon.text('chevron_left');
        }

        // Toggle the class d-none on the sidebar content
        $('#v-pills-tabContent').toggleClass('d-none');

        // Toggle the sidebar collapsed state
        $('body').toggleClass('sidebar-collapsed');

        // Adjust the canvas container when sidebar is collapsed/expanded
        if ($('#v-pills-tabContent').hasClass('d-none')) {
          // Sidebar is now collapsed - expand canvas to center and take full space
          $('.col-sm-12.col-md-5.col-lg-8').addClass('canvas-expanded');
          $(this).addClass('d-none');

          // Show the reopen sidebar button
          $('#reopen-sidebar-btn').removeClass('d-none');

          // Center the canvas by adjusting its container
          setTimeout(function() {
            // Force canvas to re-render after transition completes
            resizeCanvas(); // Use our improved resize function
          }, 300); // Match this to the CSS transition duration
        } else {
          // Sidebar is now expanded - return canvas to normal
          $('.col-sm-12.col-md-5.col-lg-8').removeClass('canvas-expanded');

          // Hide the reopen sidebar button
          $('#reopen-sidebar-btn').addClass('d-none');

          // Force canvas to re-render after transition completes
          setTimeout(function() {
            // Reset any inline styles that might have been applied
            $('.col-sm-12.col-md-5.col-lg-8').css({
              'width': '',
              'max-width': '',
              'margin': '',
              'text-align': ''
            });

            // Reset canvas-editor-main styles
            $('.canvas-editor-main').css({
              'margin': '',
              'text-align': ''
            });

            // Reset canvas wrapper styles
            if (canvas && canvas.wrapperEl) {
              canvas.wrapperEl.style.margin = '';
              canvas.wrapperEl.style.left = '';
              canvas.wrapperEl.style.transform = '';
            }

            resizeCanvas(); // Use our improved resize function
          }, 300); // Match this to the CSS transition duration
        }
      });

      // Event listener for sidebar tab buttons
      $('#v-pills-tab button').on('click', function() {
        // When a sidebar tab is clicked, make sure canvas returns to normal position
        $('.col-sm-12.col-md-5.col-lg-8').removeClass('canvas-expanded');
        $('body').removeClass('sidebar-collapsed');
        $('#reopen-sidebar-btn').addClass('d-none');

        // Force canvas to re-render after transition completes
        setTimeout(function() {
          resizeCanvas(); // Use our improved resize function
        }, 300);
      });

      // Event listener for the reopen sidebar button
      $('#openSidebarBtn').on('click', function() {
        // Show the sidebar content
        $('#v-pills-tabContent').removeClass('d-none');

        // Return canvas to normal position
        $('.col-sm-12.col-md-5.col-lg-8').removeClass('canvas-expanded');

        // Update body class
        $('body').removeClass('sidebar-collapsed');

        // Hide the reopen button
        $('#reopen-sidebar-btn').addClass('d-none');

        // Show the close button
        $('#closeSidebarBtn').removeClass('d-none');

        // Force canvas to re-render after transition completes
        setTimeout(function() {
          resizeCanvas(); // Use our improved resize function
        }, 300); // Match this to the CSS transition duration
      });

      // Call resizeCanvas on page load to ensure proper initial positioning
      setTimeout(function() {
        resizeCanvas();

        // Force a second resize after a longer delay to ensure everything is properly positioned
        setTimeout(function() {
          resizeCanvas();
        }, 1000);
      }, 500);
    });
  </script>

  <!-- collpse content sidebar ends -->


  <!-- add text starts -->
 <script>
  $(document).ready(function() {
    // Function to add text to the canvas with dynamic width
    function addTextToCanvas(text, fontSize, fontWeight) {
        var tempText = new fabric.Text(text, {
            fontSize: fontSize,
            fontWeight: fontWeight || 'normal',
            fontFamily: 'Arial'
        });

        // Calculate width based on text length and font size
        var calculatedWidth = tempText.width + 20; // Adding some padding

        var newText = new fabric.Textbox(text, {
            left: canvas.width / 2,
            top: canvas.height / 2,
            fontSize: fontSize,
            fontFamily: 'Arial',
            fill: '#333',
            fontWeight: fontWeight || 'normal',  // Default to 'normal' if not provided
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            width: calculatedWidth // Set the calculated width
        });

        canvas.add(newText);
        canvas.setActiveObject(newText);
        canvas.renderAll();
    }

    // Add H1 text (Heading 1)
    $('#addH1').click(function() {
        addTextToCanvas('Heading 1', 36, 'bold');
    });

    // Add H2 text (Heading 2)
    $('#addH2').click(function() {
        addTextToCanvas('Heading 2', 30, 'bold');
    });

    // Add H3 text (Heading 3)
    $('#addH3').click(function() {
        addTextToCanvas('Heading 3', 24, 'bold');
    });

    // Add H4 text (Heading 4)
    $('#addH4').click(function() {
        addTextToCanvas('Heading 4', 20, 'bold');
    });

    // Add H5 text (Heading 5)
    $('#addH5').click(function() {
        addTextToCanvas('Heading 5', 16, 'bold');
    });

    // Add paragraph text
    $('#addpara').click(function() {
        addTextToCanvas('Paragraph text', 14, 'normal');
    });
});

 </script>
  <!-- add text ends -->


<!-- element and shape JS starts -->
 <script>
  $(document).ready(function() {
    // Function to add a shape to the canvas
    function addShapeToCanvas(shapeType) {
        var shape;
        var canvasCenterX = canvas.width / 2;
        var canvasCenterY = canvas.height / 2;

        switch (shapeType) {
            case 'rectangle':
                shape = new fabric.Rect({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: 'grey',
                    width: 100,
                    height: 100,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'circle':
                shape = new fabric.Circle({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: 'grey',
                    radius: 50,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'triangle':
                shape = new fabric.Triangle({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: 'grey',
                    width: 100,
                    height: 100,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'ellipse':
                shape = new fabric.Ellipse({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: 'grey',
                    rx: 80,
                    ry: 60,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'line':
                shape = new fabric.Line([canvasCenterX - 50, canvasCenterY, canvasCenterX + 50, canvasCenterY], {
                    stroke: 'black',
                    strokeWidth: 2
                });
                break;
            case 'polygon':
                shape = new fabric.Polygon([
                    { x: 0, y: 0 },
                    { x: 100, y: 0 },
                    { x: 100, y: 100 },
                    { x: 0, y: 100 },
                ], {
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: 'grey',
                    originX: 'center',
                    originY: 'center'
                });
                break;
            default:
                return;
        }

        canvas.add(shape);
        canvas.setActiveObject(shape);
        canvas.renderAll();
    }

    // Event listeners for shape buttons
    $('#addRectangleBtn').click(function() {
        addShapeToCanvas('rectangle');
    });

    $('#addCircleBtn').click(function() {
        addShapeToCanvas('circle');
    });

    $('#addTriangleBtn').click(function() {
        addShapeToCanvas('triangle');
    });

    $('#addEllipseBtn').click(function() {
        addShapeToCanvas('ellipse');
    });

    $('#addLineBtn').click(function() {
        addShapeToCanvas('line');
    });

    $('#addPolygonBtn').click(function() {
        addShapeToCanvas('polygon');
    });
});



// Function to add a shape with border to the canvas
$(document).ready(function() {
    // Function to add a shape with border to the canvas
    function addShapeToCanvas(shapeType) {
        var shape;
        var canvasCenterX = canvas.width / 2;
        var canvasCenterY = canvas.height / 2;


        switch (shapeType) {
            case 'rectangle':
                shape = new fabric.Rect({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: '#007bff', // Button color
                    width: 150,
                    height: 100,
                    stroke: 'black', // Black border
                    strokeWidth: 2,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'circle':
                shape = new fabric.Circle({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: '#6c757d', // Button color
                    radius: 50,
                    stroke: 'black', // Black border
                    strokeWidth: 2,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'triangle':
                shape = new fabric.Triangle({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: '#28a745', // Button color
                    width: 100,
                    height: 100,
                    stroke: 'black', // Black border
                    strokeWidth: 2,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'ellipse':
                shape = new fabric.Ellipse({
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: '#17a2b8', // Button color
                    rx: 80,
                    ry: 60,
                    stroke: 'black', // Black border
                    strokeWidth: 2,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            case 'line':
                shape = new fabric.Line([canvasCenterX - 50, canvasCenterY, canvasCenterX + 50, canvasCenterY], {
                    stroke: '#ffc107', // Button color
                    strokeWidth: 2
                });
                break;
            case 'polygon':
                shape = new fabric.Polygon([
                    { x: 0, y: 0 },
                    { x: 100, y: 0 },
                    { x: 100, y: 100 },
                    { x: 0, y: 100 }
                ], {
                    left: canvasCenterX,
                    top: canvasCenterY,
                    fill: '#dc3545', // Button color
                    stroke: 'black', // Black border
                    strokeWidth: 2,
                    originX: 'center',
                    originY: 'center'
                });
                break;
            default:
                return;
        }


        canvas.add(shape);
        canvas.setActiveObject(shape);
        canvas.renderAll();
    }

    // Event listeners for shape buttons
    $('#addRectangleBorderedBtn').click(function() {
        addShapeToCanvas('rectangle');
    });

    $('#addCircleBorderedBtn').click(function() {
        addShapeToCanvas('circle');
    });

    $('#addTriangleBorderedBtn').click(function() {
        addShapeToCanvas('triangle');
    });

    $('#addEllipseBorderedBtn').click(function() {
        addShapeToCanvas('ellipse');
    });

    $('#addLineBorderedBtn').click(function() {
        addShapeToCanvas('line');
    });

    $('#addPolygonBorderedBtn').click(function() {
        addShapeToCanvas('polygon');
    });
});

 </script>
<!-- element and shape JS ends -->


<!-- editor toolbar starts -->
<script>
  $(document).ready(function() {


// This code has been replaced by the updated color picker implementation
// and is no longer needed




    //change bg color of canvas
    // Initialize Coloris on the hidden input
    Coloris({
        el: '#bgColorPicker', // Target the input element with ID 'bgColorPicker'
        theme: 'large',
        themeMode: 'dark', // Optional: customize the theme if desired
    });

    // On button click, trigger the Coloris input
    $('#changeBackgroundColorBtn').on('click', function () {
        $('#bgColorPicker').click(); // Open the Coloris color picker
    });

    // When a color is selected, update the canvas background
    $('#bgColorPicker').on('input', function () {
        let color = $(this).val();
        if (color) {
            // Set the background color of the Fabric.js canvas
            canvas.setBackgroundColor(color, canvas.renderAll.bind(canvas));
        }
    });


    // Initialize Coloris for color pickers
    //Coloris.init();

    Coloris({
  // The default behavior is to append the color picker's dialog to the end of the document's
  // body. but it is possible to append it to a custom parent instead. This is especially useful
  // when the color fields are in a scrollable container and you want the color picker's dialog
  // to remain anchored to them. You will need to set the CSS position of the desired container
  // to "relative" or "absolute".
  // Note: This should be a scrollable container with enough space to display the picker.
  parent: 'border-color-picker',

  // A custom selector to bind the color picker to. This must point to HTML input fields.
  el: 'border-color-picker',

  // The bound input fields are wrapped in a div that adds a thumbnail showing the current color
  // and a button to open the color picker (for accessibility only). If you wish to keep your
  // fields unaltered, set this to false, in which case you will lose the color thumbnail and
  // the accessible button (not recommended).
  // Note: This only works if you specify a custom selector to bind the picker (option above),
  // it doesn't work on the default [data-coloris] attribute selector.
  wrap: true,

  // Set to true to activate basic right-to-left support.
  rtl: false,

  // Available themes: default, large, polaroid, pill (horizontal).
  // More themes might be added in the future.
  theme: 'auto',

  // Set the theme to light or dark mode:
  // * light: light mode (default).
  // * dark: dark mode.
  // * auto: automatically enables dark mode when the user prefers a dark color scheme.
  themeMode: 'light',

  // The margin in pixels between the input fields and the color picker's dialog.
  margin: 4,

  // Set the preferred color string format:
  // * hex: outputs #RRGGBB or #RRGGBBAA (default).
  // * rgb: outputs rgb(R, G, B) or rgba(R, G, B, A).
  // * hsl: outputs hsl(H, S, L) or hsla(H, S, L, A).
  // * auto: guesses the format from the active input field. Defaults to hex if it fails.
  // * mixed: outputs #RRGGBB when alpha is 1; otherwise rgba(R, G, B, A).
  format: 'hex',

  // Set to true to enable format toggle buttons in the color picker dialog.
  // This will also force the format option (above) to auto.
  formatToggle: true,

  // Enable or disable alpha support.
  // When disabled, it will strip the alpha value from the existing color string in all formats.
  alpha: true,

  // Set to true to always include the alpha value in the color value even if the opacity is 100%.
  forceAlpha: false,

  // Set to true to hide all the color picker widgets (spectrum, hue, ...) except the swatches.
  swatchesOnly: true,

  // Focus the color value input when the color picker dialog is opened.
  focusInput: true,

  // Select and focus the color value input when the color picker dialog is opened.
  selectInput: true,

  // Show an optional clear button
  clearButton: true,

  // Set the label of the clear button
  clearLabel: 'Clear',

  // Show an optional close button
  closeButton: true,

  // Set the label of the close button
  closeLabel: 'Close',

  // An array of the desired color swatches to display. If omitted or the array is empty,
  // the color swatches will be disabled.
  swatches: [
  '#264653',   // Your colors
  '#2a9d8f',
  '#e9c46a',
  'rgb(244,162,97)',
  '#e76f51',
  '#d62828',
  'navy',
  '#07b',
  '#0096c7',
  '#00b4d880',
  'rgba(0,119,182,0.8)',

  // Google Docs border colors
  '#000000',  // Black
  '#5f6368',  // Google gray
  '#1a73e8',  // Google blue
  '#34a853',  // Google green
  '#fbbc04',  // Google yellow
  '#ea4335',  // Google red
  '#f28b82',  // Light red
  '#fbbc04',  // Light yellow
  '#fff475',  // Yellow
  '#ccff90',  // Light green
  '#a7ffeb',  // Teal
  '#d7aefb',  // Purple
  '#fdcfe8',  // Pink
  '#e6c9a8',  // Brown
  '#ffffff'   // White
],

  // Set to true to use the color picker as an inline widget. In this mode the color picker is
  // always visible and positioned statically within its container, which is by default the body
  // of the document. Use the "parent" option to set a custom container.
  // Note: In this mode, the best way to get the picked color, is listening to the "coloris:pick"
  // event and reading the value from the event detail (See example in the Events section). The
  // other way is to read the value of the input field with the id "clr-color-value".
  inline: false,

  // In inline mode, this is the default color that is set when the picker is initialized.
  defaultColor: '#e6c9a8',

  // A function that is called whenever a new color is picked. This defaults to an empty function,
  // but can be set to a custom one. The selected color is passed to the function as an argument.
  // Use in instances (described below) to perform a custom action for each instance.
  onChange: (color) => undefined
});

    // Update toolbar values based on selected object
    canvas.on('selection:updated', updateToolbar);
    canvas.on('selection:created', updateToolbar);

    function updateToolbar() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            $('#fontFamily').val(activeObject.fontFamily);
            $('#fontSizeInput').val(activeObject.fontSize);
            $('#fontSizeDropdown').val(activeObject.fontSize);
            $('#borderColorPicker').val(activeObject.stroke);
            $('#borderThickness').val(activeObject.strokeWidth);
            $('#borderType').val(activeObject.strokeDashArray ? 'dashed' : 'solid');
            $('#opacity').val(activeObject.opacity);
        }
    }

    // Font change
    $('#fontFamily').change(function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === 'textbox') {
            activeObject.set('fontFamily', $(this).val());
            canvas.renderAll();
        }
    });

    // Font size change
    $('#fontSizeInput, #fontSizeDropdown').change(function() {
        var newSize = $(this).val();
        var activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === 'textbox') {
            activeObject.set('fontSize', parseInt(newSize));
            canvas.renderAll();
        }
    });

    // Font style changes
    $('#boldBtn').click(function() {
        toggleTextStyle('fontWeight', 'bold', 'normal');
    });
    $('#italicBtn').click(function() {
        toggleTextStyle('fontStyle', 'italic', 'normal');
    });
    $('#underlineBtn').click(function() {
        toggleTextStyle('underline', true, false);
    });
    $('#strikethroughBtn').click(function() {
        toggleTextStyle('linethrough', true, false);
    });

    function toggleTextStyle(style, valueOn, valueOff) {
        var activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === 'textbox') {
            activeObject.set(style, activeObject[style] === valueOn ? valueOff : valueOn);
            canvas.renderAll();
        }
    }

    // Alignment change
    $('#alignLeftBtn, #alignCenterBtn, #alignRightBtn, #alignJustifyBtn').click(function() {
        var alignment = $(this).data('align'); // Get the alignment value from the clicked button
        var activeObject = canvas.getActiveObject();

        if (activeObject && activeObject.type === 'textbox') {
            activeObject.set({
                textAlign: alignment  // Set the alignment of the active textbox
            });
            canvas.renderAll();  // Re-render the canvas to apply changes
        }
    });


      // Add link to the selected textbox object
  $('#addLinkBtn').click(function() {
      var activeObject = canvas.getActiveObject();

      if (activeObject && activeObject.type === 'textbox') {
          // Prompt user for the URL
          var link = prompt('Enter the URL for the link:');

          if (link) {
              // Add link to the textbox object
              activeObject.set('link', link);  // Store the link in a custom property

              // Optionally change the text appearance to indicate it's a link
              activeObject.set({
                  fill: 'blue',
                  underline: true
              });

              canvas.renderAll();  // Re-render the canvas to apply changes
          }
      } else {
          alert('Please select a text object to add a link.');
      }
  });



    // Border color and thickness change
    $('#borderColorPicker').on('input', function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            activeObject.set('stroke', $(this).val());
            canvas.renderAll();
        }
    });

    // Font color change
    $('#fontColorPicker').on('input', function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject && activeObject.type === 'textbox') {
            activeObject.set('fill', $(this).val());  // Set the font color
            canvas.renderAll();  // Re-render the canvas to apply the changes
        }
    });


    $('#borderThickness').change(function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            activeObject.set('strokeWidth', parseInt($(this).val()));
            canvas.renderAll();
        }
    });

    // Border type change
    $('#borderType').change(function() {
        var borderType = $(this).val();
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            if (borderType === 'dashed') {
                activeObject.set('strokeDashArray', [5, 5]);
            } else if (borderType === 'dotted') {
                activeObject.set('strokeDashArray', [1, 2]);
            } else {
                activeObject.set('strokeDashArray', null);
            }
            canvas.renderAll();
        }
    });

    // Lock/Unlock object
    $('#lockBtn').click(function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            activeObject.set({ selectable: false, evented: false });
            canvas.renderAll();
        }
    });

    //unlock button
    $('#unlockBtn').click(function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            activeObject.set({ selectable: true, evented: true });
            canvas.renderAll();
        }
    });

    // Opacity change
    $('#opacity').on('input', function() {
        var activeObject = canvas.getActiveObject();
        if (activeObject) {
            activeObject.set('opacity', parseFloat($(this).val()));
            canvas.renderAll();
        }
    });

    // Add custom variable
    $('#addVariableBtn').click(function() {
        var variableName = $('#variableName').val();
        var newText = new fabric.Textbox('{{' + variableName + '}}', {
            left: canvas.width / 2,
            top: canvas.height / 2,
            fontSize: 20,
            fontFamily: 'Arial',
            fill: '#333',
            originX: 'center',
            originY: 'center',
            //data: 'variable' // Store custom variable data
            data: { variable: variableName } // Store custom variable data

        });
        canvas.add(newText);
        canvas.renderAll();
    });





});

</script>
<!-- editor toolbar ends -->

<!-- crop image starts -->
 <script>
  let croppingRect, selectedImage, confirmCropBtn = $('#confirmCropBtn');

function enableCropMode() {
    const activeObject = canvas.getActiveObject();

    // Check if the selected object is an image
    if (activeObject && activeObject.type === 'image') {
        selectedImage = activeObject;

        // Create a cropping rectangle overlay on top of the selected image
        croppingRect = new fabric.Rect({
            left: selectedImage.left,
            top: selectedImage.top,
            width: selectedImage.width * selectedImage.scaleX,
            height: selectedImage.height * selectedImage.scaleY,
            fill: 'rgba(0,0,0,0.1)', // semi-transparent overlay
            stroke: 'black',
            strokeDashArray: [5, 5],
            originX: 'left',
            originY: 'top',
            selectable: true,
            hasBorders: true,
            hasControls: true,
        });

        canvas.add(croppingRect);
        canvas.setActiveObject(croppingRect);
        canvas.renderAll();

        // Show the confirm crop button right below the cropping rectangle
        const canvasOffset = $('#canvas-editor').offset();
        const btnTop = canvasOffset.top + croppingRect.top + croppingRect.height + 10;
        const btnLeft = canvasOffset.left + croppingRect.left;
        confirmCropBtn.css({
            display: 'block',
            top: btnTop + 'px',
            left: btnLeft + 'px'
        });
    } else {
        alert("Please select an image to crop.");
    }
}

function cropImage() {
    if (croppingRect && selectedImage) {
        const croppedCanvas = new fabric.Canvas();

        // Calculate the crop area based on the croppingRect's dimensions
        const left = croppingRect.left - selectedImage.left;
        const top = croppingRect.top - selectedImage.top;
        const width = croppingRect.width;
        const height = croppingRect.height;

        // Create a temporary canvas for cropping
        croppedCanvas.setDimensions({
            width: width,
            height: height
        });

        // Clone and add the image to the temporary canvas for cropping
        selectedImage.clone(function(cloneImg) {
            cloneImg.set({
                left: -left,
                top: -top
            });
            croppedCanvas.add(cloneImg);
            croppedCanvas.renderAll();

            // Get the cropped data URL
            const croppedDataUrl = croppedCanvas.toDataURL({
                left: 0,
                top: 0,
                width: width,
                height: height
            });

            // Replace the original image with the cropped image
            fabric.Image.fromURL(croppedDataUrl, function(croppedImg) {
                croppedImg.set({
                    left: selectedImage.left,
                    top: selectedImage.top,
                    scaleX: 1,
                    scaleY: 1,
                    originX: 'center',
                    originY: 'center'
                });

                // Remove the old image and crop rectangle
                canvas.remove(selectedImage);
                canvas.remove(croppingRect);

                // Add the cropped image to the canvas
                canvas.add(croppedImg);
                canvas.setActiveObject(croppedImg);
                canvas.renderAll();

                // Hide the confirm crop button
                confirmCropBtn.hide();
            });
        });
    }
}

// Crop button click event
$(document).ready(function() {
    $('#cropImageBtn').click(function() {
        enableCropMode();
    });

    // Add listener for cropping when user clicks Confirm Crop
    confirmCropBtn.click(function() {
        cropImage();
    });
});

 </script>
<!-- crop image ends -->





<!-- keyboard shortcuts starts -->

<!-- Toast Container -->
<div id="toast" class="toast">
  <div class="toast-body"></div>
</div>


<!-- save history of canvas for shortcuts starts -->
<script>
  $(document).ready(function() {
  var clipboard = null;
  var canvas = window.canvas; // assuming `canvas` is already initiated globally
  var undoStack = [];
  var redoStack = [];

  // Function to show toast notification
  function showToast(message) {
    var $toast = $('#toast');
    $toast.find('.toast-body').text(message);
    var toast = new bootstrap.Toast($toast);
    toast.show();
}

  // Function to save the current state to undo stack
  function saveState() {
    undoStack.push(JSON.stringify(canvas));
  }

  // Undo function
  function undoLastAction() {

    if (undoStack.length > 0) {
      redoStack.push(JSON.stringify(canvas));
      var lastState = undoStack.pop();
      canvas.loadFromJSON(lastState, function() {
        canvas.renderAll();
        showToast('Undo performed');
      });
    } else {
      showToast('No actions to undo');
    }
  }

  // Redo function
  function redoLastAction() {
    if (redoStack.length > 0) {
      undoStack.push(JSON.stringify(canvas));
      var redoState = redoStack.pop();
      canvas.loadFromJSON(redoState, function() {
        canvas.renderAll();
        showToast('Redo performed');
      });
    } else {
      showToast('No actions to redo');
    }
  }

  // Copy selected object
  function copyObject() {
    if (canvas.getActiveObject()) {
      canvas.getActiveObject().clone(function(cloned) {
        clipboard = cloned;
        showToast('Object copied');
      });
    }
  }


// Existing function to paste a copied object from clipboard
function pasteObject() {
    if (clipboard) {
        clipboard.clone(function (clonedObj) {
            canvas.discardActiveObject();
            clonedObj.set({
                left: clonedObj.left + 10,
                top: clonedObj.top + 10,
                evented: true,
            });
            canvas.add(clonedObj);
            canvas.setActiveObject(clonedObj);
            canvas.renderAll();
            saveState(); // Save the new state
            showToast('Object pasted');
        });
    }
}


  // Add event listener for paste event
      // Add event listener for paste event
      $(document).on('paste', function (e) {
        var clipboardData = e.originalEvent.clipboardData || window.clipboardData;

        // Flag to track if external content was handled
        var isExternalContentHandled = false;

        if (clipboardData && clipboardData.items.length > 0) {
            for (var i = 0; i < clipboardData.items.length; i++) {
                var item = clipboardData.items[i];

                if (item.type.indexOf('image') !== -1) {
                    // Handle image paste
                    var blob = item.getAsFile();
                    var reader = new FileReader();

                    reader.onload = function (event) {
                        var imgObj = new Image();
                        imgObj.src = event.target.result;

                        imgObj.onload = function () {
                            var image = new fabric.Image(imgObj);
                            image.set({
                                left: 100, // Default position for image
                                top: 100
                            });
                            canvas.add(image);
                            canvas.renderAll();
                            saveState(); // Save state after adding image
                            showToast('Image pasted');
                        };
                    };

                    reader.readAsDataURL(blob);
                    isExternalContentHandled = true; // Mark as handled
                    break;
                } else if (item.kind === 'string' && item.type === 'text/plain') {
                    // Handle text paste
                    item.getAsString(function (text) {
                        var textObj = new fabric.Textbox(text, {
                            left: 100, // Default position for text
                            top: 100,
                            fontSize: 20,
                            editable: true, // Ensure text is editable
                            objectCaching: false // Disable object caching to avoid issues with editing
                        });

                        canvas.add(textObj);
                        canvas.setActiveObject(textObj); // Make it the active object
                        textObj.enterEditing(); // Enter editing mode immediately
                        canvas.renderAll();
                        saveState(); // Save state after adding text
                        showToast('Text pasted');
                    });
                    isExternalContentHandled = true; // Mark as handled
                    break;
                }
            }
        }

        // Only use the internal clipboard (pasteObject) if no external content was pasted
        if (!isExternalContentHandled && clipboard) {
            pasteObject();
        }
    });

  // Cut selected object
// Cut selected object or selected text if editing text
function cutObject() {
    const activeObject = canvas.getActiveObject();

    if (activeObject && activeObject.isEditing) {
        // If editing text, cut selected text
        const selectedText = activeObject.getSelectedText();
        if (selectedText) {
            // Cut the selected text from the text object
            const start = activeObject.selectionStart;
            const end = activeObject.selectionEnd;
            const newText = activeObject.text.slice(0, start) + activeObject.text.slice(end);
            activeObject.text = newText;
            activeObject.selectionStart = activeObject.selectionEnd = start;
            canvas.renderAll();

            // Copy the selected text to clipboard
            navigator.clipboard.writeText(selectedText).then(() => {
                showToast('Text cut');
            });
        }
    } else if (activeObject) {
        // If not editing text, cut the whole object
        copyObject(); // Copy the object first
        canvas.remove(activeObject); // Then remove it
        showToast('Object cut');
    }
}


  // Select all objects
  function selectAllObjects() {
    canvas.discardActiveObject();
    var sel = new fabric.ActiveSelection(canvas.getObjects(), {
      canvas: canvas
    });
    canvas.setActiveObject(sel);
    canvas.renderAll();
    showToast('All objects selected');
  }

  // Save canvas as image (simplified save function)
  function saveCanvas() {
    var dataURL = canvas.toDataURL({
      format: 'png',
      quality: 0.8
    });
    var link = document.createElement('a');
    link.href = dataURL;
    link.download = 'image.png';
    link.click();
    showToast('Canvas saved');
    console.log("canvas saved")
  }

  // Lock selected object
  function lockObject() {
    var activeObject = canvas.getActiveObject();
    if (activeObject) {
      activeObject.set({
        selectable: false,
        evented: false,
      });
      canvas.renderAll();
      showToast('Object locked');
    }
  }

  // Unlock selected object
  function unlockObject() {
    var activeObject = canvas.getActiveObject();
    if (activeObject) {
      activeObject.set({
        selectable: true,
        evented: true,
      });
      canvas.renderAll();
      showToast('Object unlocked');
    }
  }

  // Duplicate object with Alt/Option drag
  let cloneCreated = false; // Track if a clone has already been created

// Duplicate object with Alt/Option drag
canvas.on('object:moving', function(e) {
  if (e.e.altKey && !cloneCreated) {
    e.target.clone(function(clone) {
      clone.set({
        left: e.target.left + 10,
        top: e.target.top + 10
      });
      canvas.add(clone);
      cloneCreated = true; // Mark that a clone has been created
      showToast('Object duplicated');
    });
  }

  // Reset cloneCreated flag when Alt is released or dragging stops
  canvas.on('mouse:up', function() {
    cloneCreated = false;
  });
});


  // Keyboard shortcuts implementation
  $(document).keydown(function(e) {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'z': // Undo
          if (e.shiftKey) {
            redoLastAction();
          } else {
            undoLastAction();
          }
          break;
        case 'y': // Redo
          redoLastAction();
          break;
        case 'c': // Copy
          copyObject();
          break;
        case 'v': // Paste
          pasteObject();
          break;
        case 'x': // Cut
          cutObject();
          break;
        case 'a': // Select All
          selectAllObjects();
          break;
        case 's': // Save
          e.preventDefault();
          saveCanvas();
          break;
        case 'l': // Lock
          lockObject();
          break;
      }
    }
  });


  // Add more functions toggle options for bold, underline, strikethrough, hyperlink etc.



// Function to delete selected object(s) when 'Delete' or 'Backspace' is pressed
$(document).keydown(function (e) {
    // Check if the key pressed is 'Delete' or 'Backspace'
    if (e.key === 'Delete' || e.key === 'Backspace') {
        // Prevent deleting objects if typing in input fields or textareas
        if ($(e.target).is('input, textarea') || e.target.isContentEditable) {
            return; // Stop execution if editing text
        }

        // Get all selected objects (can be one or multiple)
        var activeObjects = canvas.getActiveObjects();

        if (activeObjects.length) {
            // Iterate over all selected objects and remove them
            activeObjects.forEach(function (obj) {
                canvas.remove(obj);
            });

            // Clear selection and re-render the canvas
            canvas.discardActiveObject();
            canvas.renderAll();
        }
    }
});
});



//buttons to undo and redo
$(document).ready(function() {
        $('#undoButton').click(function() {
            undoLastAction();
        });

        $('#redoButton').click(function() {
            redoLastAction();
        });
    });
//buttons to undo and redo

</script>
<!-- save history of canvas for shortcuts emds -->
<!-- keyboard shortcuts ends -->



<!-- fetch template starts-->
 <script>

  var designQuery=$("#designSearchQuery").val()

  //alert(designQuery)
  //var designQuery='certificate'

  function fetchTemplateBackgrounds() {
    $.ajax({
        url: `/api/files/search?tags=${designQuery},background`,
        method: 'GET',
        success: function(data) {
          //alert("data received from query",designQuery)
          const $templateContainer = $('#designSearchResults');
            $templateContainer.empty(); // Clear existing templates

            data.forEach(file => {
                // Create template wrapper
                const $templateWrapper = $('<div>', { class: 'template-wrapper', style: 'position: relative; display: inline-block; margin: 5px;' });

                // Create image element for the template
                const $templateImage = $('<img>', {
                    class: "my-template-image",
                    src: file.url,
                    alt: file.fileName,
                    style: 'max-width: 150px;'
                });

                // Create download button
                const $downloadBtn = $('<button>', {
                    class: 'btn btn-sm btn-secondary download-template-btn',
                    html: '<i class="fas fa-download"></i>',
                    style: 'position: absolute; top: 10px; right: 40px; display: none;'
                }).on('click', function() {
                    const $a = $('<a>', {
                        href: file.url,
                        download: file.fileName
                    });
                    $a[0].click(); // Trigger the download programmatically
                });

                // Create delete button
                const $deleteBtn = $('<button>', {
                    class: 'btn btn-sm btn-secondary delete-template-btn',
                    html: '<i class="fas fa-trash-alt"></i>',
                    style: 'position: absolute; top: 10px; right: 10px; display: none;'
                }).on('click', function() {
                    if (confirm('Are you sure you want to delete this template?')) {
                        $.ajax({
                            url: `/api/files/${file._id}`,
                            method: 'DELETE',
                            success: function() {
                                fetchTemplates(); // Refresh template list after delete
                            },
                            error: function(err) {
                                console.error('Error deleting template:', err);
                            }
                        });
                    }
                });

                // Append image and buttons to wrapper
                $templateWrapper.append($templateImage, $downloadBtn, $deleteBtn);
                $templateContainer.append($templateWrapper);

                // Show buttons on hover
                $templateWrapper.hover(
                    function() { $downloadBtn.show(); $deleteBtn.show(); },
                    function() { $downloadBtn.hide(); $deleteBtn.hide(); }
                );
            });


            //add background on canvas but save the image as base64 in cavas object
            // Add to canvas on click
$(".my-template-image").on('click', function () {
    var imgSrc = $(this).attr('src');

    // Create an image element to load the image and convert to base64
    var imgElement = new Image();
    imgElement.crossOrigin = 'anonymous'; // Handle CORS for cross-origin images
    imgElement.src = imgSrc;

    // When the image is loaded
    imgElement.onload = function () {
        // Create a canvas to get the base64 data
        var tempCanvas = document.createElement('canvas');
        var tempCtx = tempCanvas.getContext('2d');

        // Set canvas size to image size
        tempCanvas.width = imgElement.width;
        tempCanvas.height = imgElement.height;

        // Draw the image on the temporary canvas
        tempCtx.drawImage(imgElement, 0, 0);

        // Convert to base64
        var base64Image = tempCanvas.toDataURL('image/png');

        // Use fabric.Image.fromURL to add to Fabric.js canvas
        fabric.Image.fromURL(base64Image, function (img) {
            // Scale the image to cover the entire canvas
            const canvasAspect = canvas.width / canvas.height;
            const imgAspect = img.width / img.height;

            if (canvasAspect >= imgAspect) {
                // Scale to match the canvas width
                img.scaleToWidth(canvas.width);
            } else {
                // Scale to match the canvas height
                img.scaleToHeight(canvas.height);
            }

            // Set the image as the background image and cover the canvas
            canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                originX: 'left',
                originY: 'top'
            });
        });
    };
});


            // Add to canvas on click as baground image
            $(".my-template-image-----imageurl").on('click', function () {
                var imgSrc = $(this).attr('src');


                fabric.Image.fromURL(imgSrc, function (img) {
                    // Scale the image to cover the entire canvas
                    const canvasAspect = canvas.width / canvas.height;
                    const imgAspect = img.width / img.height;

                    if (canvasAspect >= imgAspect) {
                        // Scale to match the canvas width
                        img.scaleToWidth(canvas.width);
                    } else {
                        // Scale to match the canvas height
                        img.scaleToHeight(canvas.height);
                    }

                    // Set the image as the background image and cover the canvas
                    canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas), {
                        originX: 'left',
                        originY: 'top',
                        crossOrigin: 'anonymous'
                    });
                }, { crossOrigin: 'anonymous' });

            });


        },
        error: function(err) {
            console.error('Error fetching templates:', err);
        }
    });
}

// Call the function on page load
$(document).ready(function() {
  fetchTemplateBackgrounds();
});

//make the search work
$("#designSearchButton").on("click",()=>{
  //alert("design search button clicked")
  fetchTemplateBackgrounds()

});

 </script>
<!-- fetch template ends-->


<!-- download handler starts -->

<script>
  const { jsPDF } = window.jspdf;
// Common function to scale and restore canvas
function exportCanvas(format, quality, isForPrint) {
    var dpi = isForPrint ? 300 : 72; // 300 DPI for print, 72 DPI for normal
    var scaleFactor = dpi / 72;

    // Store original dimensions
    var originalWidth = canvas.width;
    var originalHeight = canvas.height;

    // Scale the canvas dimensions
    canvas.setDimensions({
        width: originalWidth * scaleFactor,
        height: originalHeight * scaleFactor
    });

    // Set zoom to scaleFactor for accurate export
    canvas.setZoom(scaleFactor);

    // Export logic based on the format
    if (format === 'png' || format === 'jpeg') {
        var dataURL = canvas.toDataURL({
            format: format,
            quality: quality || 1 // Use provided quality or default to 1
        });

        // Trigger download of the image
        var link = document.createElement('a');
        link.href = dataURL;
        link.download = `canvas_export.${format}`;
        link.click();
    } else if (format === 'pdf') {
        // Export as PDF
        var pdf = new jsPDF('landscape', 'pt', [originalWidth, originalHeight]);

        // Add the image to the PDF with the same DPI scaling
        var imgData = canvas.toDataURL({ format: 'png' });
        pdf.addImage(imgData, 'PNG', 0, 0, originalWidth, originalHeight);
        pdf.save('canvas_export.pdf');
    }

    // Restore original canvas dimensions and zoom
    canvas.setDimensions({
        width: originalWidth,
        height: originalHeight
    });
    canvas.setZoom(1);
}

// Handle Download PNG, JPEG, PDF buttons
$('#downloadPngBtn').click(function () {
    exportCanvas('png', 1, false); // Export PNG (72 DPI)
});

$('#downloadJpegBtn').click(function () {
    exportCanvas('jpeg', 1, false); // Export JPEG (72 DPI)
});

$('#downloadPdfBtn').click(function () {
    exportCanvas('pdf', null, false); // Export PDF (72 DPI)
});

// Handle high-DPI (300 DPI) export for print-quality images
$('#printDownloadPngBtn').click(function () {
    exportCanvas('png', 1, true); // Export PNG (300 DPI)
});

$('#printDownloadJpegBtn').click(function () {
    exportCanvas('jpeg', 1, true); // Export JPEG (300 DPI)
});

$('#printDownloadPdfBtn').click(function () {
    exportCanvas('pdf', null, true); // Export PDF (300 DPI)
});
</script>

<!-- download handler ends -->



<!-- HQ Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
      <div class="modal-content">
          <div class="modal-header">
              <h5 class="modal-title" id="previewModalLabel">Upscaled Canvas Preview</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body text-center">
              <img id="upscaledPreview" class="img-fluid" alt="Upscaled Preview">
          </div>
          <div class="modal-footer">
              <button id="downloadBtn" class="btn btn-success">Download</button>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
      </div>
  </div>
</div>

<!-- variable script starts -->
<script>
  $(document).ready(function() {
//     const predefinedVariables = [
//   { name: "fullName", tag: "{{fullName}}" },
//   { name: "Recipient Name", tag: "{{recipient_name}}" },
//   { name: "Certificate ID", tag: "{{certificate_id}}" },
//   { name: "Issue Date", tag: "{{issue_date}}" },
//   { name: "Expiration Date", tag: "{{expiration_date}}" },
//   { name: "Award Title", tag: "{{award_title}}" },
//   { name: "Issuer Name", tag: "{{issuer_name}}" },
//   { name: "Issuer Signature", tag: "{{issuer_signature}}" },
//   { name: "Course Name", tag: "{{course_name}}" },
//   { name: "Completion Date", tag: "{{completion_date}}" },
//   { name: "Award Description", tag: "{{award_description}}" },
//   { name: "Issuer Organization", tag: "{{issuer_organization}}" },
//   { name: "Certificate Level", tag: "{{certificate_level}}" },
//   { name: "Course Duration", tag: "{{course_duration}}" },
//   { name: "Recipient Email", tag: "{{recipient_email}}" },
//   { name: "Grade/Score", tag: "{{grade}}" },
//   { name: "Location", tag: "{{location}}" },
//   { name: "Event Date", tag: "{{event_date}}" },
//   { name: "Event Name", tag: "{{event_name}}" },
//   { name: "Certification URL", tag: "{{certificate_url}}" },
//   { name: "Verifier Name", tag: "{{verifier_name}}" },
//   { name: "Issuer Role", tag: "{{issuer_role}}" },
//   { name: "Organization Logo", tag: "{{organization_logo}}" },
//   { name: "Certificate Type", tag: "{{certificate_type}}" },
//   { name: "Recipient Role", tag: "{{recipient_role}}" },
//   { name: "Honors/Achievements", tag: "{{honors}}" },
//   { name: "Custom Message", tag: "{{custom_message}}" },
//   { name: "Awarding Institution", tag: "{{awarding_institution}}" },
//   { name: "Verification Code", tag: "{{verification_code}}" },
//   { name: "Completion Percentage", tag: "{{completion_percentage}}" },
//   { name: "Validation URL", tag: "{{validation_url}}" },
//   { name: "Course Credits", tag: "{{course_credits}}" },
//   { name: "Award Level", tag: "{{award_level}}" },
//   { name: "Date of Birth", tag: "{{date_of_birth}}" },
//   { name: "Instructor Name", tag: "{{instructor_name}}" },
//   { name: "Instructor Signature", tag: "{{instructor_signature}}" },
//   { name: "Program Coordinator", tag: "{{program_coordinator}}" },
//   { name: "Volunteer Hours", tag: "{{volunteer_hours}}" },
//   { name: "Project Title", tag: "{{project_title}}" },
//   { name: "Project Description", tag: "{{project_description}}" },
//   { name: "Supervisor Name", tag: "{{supervisor_name}}" },
//   { name: "Program Name", tag: "{{program_name}}" },
//   { name: "Program Duration", tag: "{{program_duration}}" },
//   { name: "Certificate Theme", tag: "{{certificate_theme}}" },
//   { name: "Completion Status", tag: "{{completion_status}}" },
//   { name: "Training Hours", tag: "{{training_hours}}" },
//   { name: "Registration Number", tag: "{{registration_number}}" },
//   { name: "Badge Title", tag: "{{badge_title}}" },
//   { name: "Badge Description", tag: "{{badge_description}}" },
//   { name: "Accreditation Body", tag: "{{accreditation_body}}" },
//   { name: "Accreditation Number", tag: "{{accreditation_number}}" },
//   { name: "Membership Level", tag: "{{membership_level}}" },
//   { name: "Certificate Footer Message", tag: "{{footer_message}}" },
//   { name: "Certificate QR Code", tag: "{{qr_code}}" },
//   { name: "Event Coordinator Name", tag: "{{event_coordinator_name}}" },
//   { name: "Event Coordinator Signature", tag: "{{event_coordinator_signature}}" },
//   { name: "Training Provider Name", tag: "{{training_provider_name}}" },
//   { name: "Training Provider Logo", tag: "{{training_provider_logo}}" },
//   { name: "Completion Time", tag: "{{completion_time}}" },
//   { name: "Special Recognition", tag: "{{special_recognition}}" },
//   { name: "Issued By", tag: "{{issued_by}}" }
// ];


let predefinedVariables = [
  { name: "fullName", tag: "{{fullName}}" },
  { name: "businessEmail", tag: "{{businessEmail}}" },
  { name: "jobTitle", tag: "{{jobTitle}}" },
  { name: "companyName", tag: "{{companyName}}" },
  { name: "phoneNumber", tag: "{{phoneNumber}}" },
  { name: "contactStatus", tag: "{{contactStatus}}" },
  { name: "leadSource", tag: "{{leadSource}}" },
  { name: "preferredContactMethod", tag: "{{preferredContactMethod}}" },
  { name: "notes", tag: "{{notes}}" },
  { name: "tags", tag: "{{tags}}" }
];
 function fetchCustomVariables() {
      $.ajax({
        url: "/api/custom-variable/get",
        method: "GET",
        success: function (response) {
          console.log(response, "res");
          const variables = response.contactCustomVariable;
          let structuredVariables=Object.keys(variables).map((key)=>({name:key,tag:`{{${key}}}`}));
          predefinedVariables=[...predefinedVariables,...structuredVariables]
          predefinedVariables.forEach(renderComponent);
           },
            error: function (error) {
          showToast(error.responseJSON.error, "danger");
           },
        });
    }
    fetchCustomVariables();
      let customVariables = [];

    // 0. Search Bar Functionality
    $('#search').on('input', function() {
      const query = $(this).val().toLowerCase();
      $('#components-list').empty();
      predefinedVariables.filter(item => item.name.toLowerCase().includes(query.toLowerCase())).forEach(renderComponent);
    });

    // 1. Render Predefined Variables in List
    function renderComponent(variable) {
      const item = $(`<div class="draggable-item"><button class="btn btn-secondary btn-dark">${variable.name}<button></div>`);
      item.data('tag', variable.tag);

      // 2. Click to Add Component to Center of Canvas
      item.on('click', function() {
        addVariableToCanvas($(this).data('tag'), variable.name);
      });

      // Add to the component list
      $('#components-list').append(item);
    }

   

    // 3. Add Variable to Canvas Function
    function addVariableToCanvas(tag, name, isCustom = false) {
      const textObj = new fabric.Text(name, {
        left: canvas.width / 2 - 50,
        top: canvas.height / 2 - 25,
        fill: '#000',
        fontSize: 20,
        originX: 'center',
        originY: 'center',
        text: tag,
        variable: 'true',
        isCustom: isCustom ? true : false // 3. Track if custom variable
      });

      canvas.add(textObj);
      console.log('Canvas Variables:', getCanvasVariables());
    }

    // 4. Fetch all variables and custom variables on canvas
    function getCanvasVariables() {
      return canvas.getObjects().map(obj => ({
        tag: obj.variable,
        isCustom: obj.isCustom || false
      }));
    }

    // 5. Open Modal for Custom Variable
    $('#add-variable-btn').on('click', function() {
      $('#variableModal').modal('show');
    });

    // Save Custom Variable from Modal
    $('#save-variable-btn').on('click', function() {
      const name = $('#variable-name').val();
      const type = $('#variable-type').val();
      const defaultValue = $('#variable-default').val();

      if (name && type) {
        const tag = `{{${name}}}`;
        customVariables.push({ name, type, defaultValue, tag });
        addVariableToCanvas(tag, name, true); // Save it as a custom variable
        $('#variableModal').modal('hide');
      } else {
        alert('Please provide name and type.');
      }
    });
  });
</script>
<!-- variable end  -->


<!-- custom variable modal starts -->

<!-- Modal for Adding Custom Variable -->
<div class="modal fade" id="variableModal" tabindex="-1" aria-labelledby="variableModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="variableModalLabel">Add Custom Variable</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="custom-variable-form">
          <div class="mb-3">
            <label for="variable-name" class="form-label">Variable Name</label>
            <input type="text" id="variable-name" class="form-control" required>
          </div>
          <div class="mb-3">
            <label for="variable-type" class="form-label">Variable Type</label>
            <select id="variable-type" class="form-select" required>
              <option value="text">Text</option>
              <option value="number">Number</option>
              <option value="date">Date</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="variable-default" class="form-label">Default Value</label>
            <input type="text" id="variable-default" class="form-control">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" id="save-variable-btn" class="btn btn-primary">Save Variable</button>
      </div>
    </div>
  </div>
</div>

<!-- custom variable modal ends -->


<!-- design thumbnail file saving starts -->
<script>
  const dataURL = canvas.toDataURL({
  format: 'jpeg',
  quality: 0.8  // Adjust the quality if needed
});

  const uploadImage = (dataURL) => {
return new Promise((resolve, reject) => {
  // Convert the data URL to a Blob
  const byteString = atob(dataURL.split(',')[1]);
  const mimeString = dataURL.split(',')[0].split(':')[1].split(';')[0];
  const arrayBuffer = new ArrayBuffer(byteString.length);
  const intArray = new Uint8Array(arrayBuffer);

  for (let i = 0; i < byteString.length; i++) {
    intArray[i] = byteString.charCodeAt(i);
  }

  const blob = new Blob([arrayBuffer], { type: mimeString });

  // Create FormData object to send the file
  const formData = new FormData();
  formData.append('file', blob, 'design.jpg');

  // AJAX request to upload the image
  $.ajax({
    url: '/api/files/upload/designs',
    type: 'POST',
    data: formData,
    processData: false,
    contentType: false,
    success: function (response) {
      if (response.file && response.file.url) {
        resolve(response.file.url);  // Resolve the promise with the file URL
      } else {
        reject('File upload failed. No URL returned.');
      }
    },
    error: function (xhr, status, error) {
      reject(`Image upload failed: ${error}`);
    }
  });
});
};
const uploadCanvasImage = () => {
return new Promise(async (resolve, reject) => {
  try {
    //const canvas = new fabric.Canvas('canvas-editor');

    // Get the canvas content as a JPEG
    const dataURL = canvas.toDataURL({ format: 'jpeg', quality: 0.8 });

    // Upload the image and get the URL
    const fileUrl = await uploadImage(dataURL);
    resolve(fileUrl);  // Return the uploaded file URL

  } catch (error) {
    reject(`Error uploading canvas image: ${error}`);
  }
});
};

// Call this function and handle the URL
// uploadCanvasImage()
// .then((url) => {
//   console.log('Image uploaded successfully at:', url);
//   // You can now use the URL (e.g., display it or save it somewhere)
// })
// .catch((error) => {
//   console.error(error);
// });

</script>
<!-- design thumbnail file saving ends -->



<!-- make arrow shortcuts starts -->
 <script>
  $(document).ready(function () {
    $(document).on('keydown', function (e) {
        const activeObjects = canvas.getActiveObjects(); // Get all selected objects

        if (activeObjects.length === 0) {
            return; // If no objects are selected, let the default behavior happen (like page scrolling)
        }

        let step = 5; // Define the step size (how many pixels to move the objects)

        // Move objects based on arrow key pressed
        switch (e.key) {
            case 'ArrowUp':
                activeObjects.forEach(function (obj) {
                    obj.top = (obj.top || 0) - step; // Move object up
                    obj.setCoords(); // Update object coordinates
                });
                break;
            case 'ArrowDown':
                activeObjects.forEach(function (obj) {
                    obj.top = (obj.top || 0) + step; // Move object down
                    obj.setCoords(); // Update object coordinates
                });
                break;
            case 'ArrowLeft':
                activeObjects.forEach(function (obj) {
                    obj.left = (obj.left || 0) - step; // Move object left
                    obj.setCoords(); // Update object coordinates
                });
                break;
            case 'ArrowRight':
                activeObjects.forEach(function (obj) {
                    obj.left = (obj.left || 0) + step; // Move object right
                    obj.setCoords(); // Update object coordinates
                });
                break;
            default:
                return; // Exit if not an arrow key
        }

        // Prevent default behavior of arrow keys (like scrolling) only when objects are selected
        e.preventDefault();

        // Re-render the canvas to reflect changes
        canvas.renderAll();
    });
});

 </script>
<!-- make arrow shortcuts ends -->



</body>

</html>