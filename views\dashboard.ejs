<%- contentFor('HeaderCss') %>
<%-include("partials/title-meta", {"title": "Dashboard" }) %>
<!-- plugin css -->
<link href="/assets/libs/admin-resources/jquery.vectormap/jquery-jvectormap-1.2.2.css" rel="stylesheet" type="text/css" />

<!-- New dashboard style starts -->
<style>
.card-title {
  font-size: 1rem;
}
.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
}
.stat-percentage {
  font-size: 0.875rem;
  color: #6c757d;
}
.apexcharts-canvas {
  margin-top: -20px;
}
.progress {
  height: 20px;
}
</style>

<!-- New dashboard style ends -->
<%- contentFor('body') %>
<%-include("partials/page-title", {"title": "Dashboard" , "pagetitle": "Dashboard" }) %>


<!-- 1st Row highlighted metric starts -->
   <div class="row g-3 ">
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Total Contacts</h5>
                <div class="stat-value" id="contactCount">0</div>
                <div class="progress mt-2">
                    <div id="dashboard-contacts" class="progress-bar bg-primary" role="progressbar" style="width: 0%" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">0%</div>
                </div>
                <div class="d-none stat-percentage mt-1">28% more from last week</div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Total Certificates</h5>
                <div class="stat-value" id="certificateCount">0</div>
                <div class="progress mt-2">
                    <div id="dashboard-certificates" class="progress-bar bg-primary" role="progressbar" style="width: 0%" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">0%</div>
                </div>
                <div class="d-none stat-percentage mt-1">28% more from last week</div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Total Badges</h5>
                <div class="stat-value" id="badgesCount">0</div>
                <div class="progress mt-2">
                    <div id="dashboard-badges" class="progress-bar bg-primary" role="progressbar" style="width: 0%" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">0%</div>
                </div>
                <div class="d-none stat-percentage mt-1">28% more from last week</div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Total Users</h5>
                <div class="stat-value" id="usersCount">0</div>
                <div class="progress mt-2">
                    <div id="dashboard-users" class="progress-bar bg-primary" role="progressbar" style="width: 0%" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">0%</div>
                </div>
                <div class="d-none stat-percentage mt-1">34% more from last week</div>
            </div>
        </div>
    </div>

</div>

<!-- 1st Row highlighted metric ends -->


<!-- 2nd row Certificate stats starts-->
<div class="row">
  <!-- end of chart -->

<!-- Payment Record Starts -->
  <div class="col-lg-8">
    <div class="card">
        <div class="card-body">
            <div class="d-flex flex-wrap align-items-center mb-4">
              <h5 class="card-title me-2">Certificates Generated</h5>
              <div class="ms-auto">
                <select class="form-select form-select-sm filter-button">
                  <option data-filter="this-year" selected>This Year</option>
                  <option data-filter="last-year">Last Year</option>
                  <option data-filter="last-12-months">Last 12 Months</option>
                  <option data-filter="last-5-years">Last 5 Years</option>
                  <option data-filter="custom">Custom Date Range</option>
              </select>
              </div>
          </div>
            <div id="certificates-generated"></div>
        </div>
    </div>
</div>
<!-- Payment Record Ends -->

  <div class="col-xl-4 d-none">
    <!-- card -->
    <div class="card">
      <!-- card body -->
      <div class="card-body">
        <div class="d-flex flex-wrap align-items-center mb-4">
          <h5 class="card-title me-2">Sales by Locations</h5>
          <div class="ms-auto">
            <div class="dropdown">
              <a class="dropdown-toggle text-reset" href="#" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <span class="text-muted font-size-12">Sort By:</span> <span class="fw-medium">World<i class="mdi mdi-chevron-down ms-1"></i></span>
              </a>

              <div class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
                <a class="dropdown-item" href="#">USA</a>
                <a class="dropdown-item" href="#">Russia</a>
                <a class="dropdown-item" href="#">Australia</a>
              </div>
            </div>
          </div>
        </div>

        <div id="sales-by-locations" data-colors='["#5156be"]' style="height: 250px"></div>

        <div class="px-2 py-2">
          <p class="mb-1">USA <span class="float-end">75%</span></p>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar progress-bar-striped bg-primary" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="75">
            </div>
          </div>

          <p class="mt-3 mb-1">Russia <span class="float-end">55%</span></p>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar progress-bar-striped bg-primary" role="progressbar" style="width: 55%" aria-valuenow="55" aria-valuemin="0" aria-valuemax="55">
            </div>
          </div>

          <p class="mt-3 mb-1">Australia <span class="float-end">85%</span></p>
          <div class="progress mt-2" style="height: 6px;">
            <div class="progress-bar progress-bar-striped bg-primary" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="85">
            </div>
          </div>
        </div>
      </div>
      <!-- end card body -->
    </div>
    <!-- end card -->
  </div>


<!-- Visitor Chart Starts -->
  <div class="col-xl-4">
    <!-- card -->
    <div class="card card-h-100">
        <!-- card body -->
        <div class="card-body">
            <div class="d-flex flex-wrap align-items-center mb-4">
                <h5 class="card-title me-2">Portal Visitor</h5>
                <div class="ms-auto">
                    <select class="d-none form-select form-select-sm">
                        <option value="MAY" selected="">May</option>
                        <option value="AP">April</option>
                        <option value="MA">March</option>
                        <option value="FE">February</option>
                        <option value="JA">January</option>
                        <option value="DE">December</option>
                    </select>
                </div>
            </div>

            <div class="row align-items-center">
                <div class="col-12">
                    <div id="visitor-overview"></div>
                <div class="resize-triggers"><div class="expand-trigger"><div style="width: 298px; height: 190px;"></div></div><div class="contract-trigger"></div></div></div>
                
                <div class="col-12 align-self-center text-center">
                    <div class="mt-4 mt-sm-0">
                        <p class="mb-1">Current Target</p>
                        <h4 id="visitorTarget">0</h4>

                        <p class="text-muted mb-4 d-none"> + ( 0.2 % ) <i class="mdi mdi-arrow-up ms-1 text-success"></i></p>

                        <div class="row g-0">
                            <div class="col-6">
                                <div>
                                    <p class="mb-2 text-muted text-uppercase font-size-11">last month</p>
                                    <h5 class="fw-medium" id="lastMonthVisitors">0</h5>
                                </div>
                            </div>
                            <div class="col-6">
                                <div>
                                    <p class="mb-2 text-muted text-uppercase font-size-11">This Month</p>
                                    <h5 class="fw-medium" id="thisMonthVisitors">0</h5>
                                </div>
                            </div>
                        </div>

                        <div class="mt-2">
                            <a href="#" class="d-none btn btn-primary btn-sm">View more <i class="mdi mdi-arrow-right ms-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Visitor Chart Ends -->


  <!-- end col -->

</div>
<!-- 2nd row - Certificate stats ends -->


<!-- 3rd row starts -->
<div class="row d-none">
  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/contacts">Total Certificates Issued</a></span>
            <h4 class="mb-3">
              <span class="counter-value" data-target="3652">0</span>
            </h4>
          </div>

          <div class="col-6">
            <div id="mini-chart1" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-success-subtle text-success">+127 added</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col -->


  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/campaigns">Campaigns</a></span>
            <h4 class="mb-3">
              <span class="counter-value" data-target="12">0</span>
            </h4>
          </div>
          <div class="col-6">
            <div id="mini-chart3" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-success-subtle text-success">+ 30%</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col -->


  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/deals">Deals</a></span>
            <h4 class="mb-3">
              <span class="counter-value" data-target="103">0</span>
            </h4>
          </div>
          <div class="col-6">
            <div id="mini-chart2" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-warning-subtle text-black">+2 new</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col-->

  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/proposals">Proposals</a></span>
            <h4 class="mb-3">
              <span class="counter-value" data-target="82">0</span>            </h4>
          </div>
          <div class="col-6">
            <div id="mini-chart3" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-success-subtle text-success">+ $12.8k</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col -->

  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/agreements">Agreements</a></span>
            <h4 class="mb-3">

              $<span class="counter-value" data-target="74.3">0</span>K

            </h4>
          </div>
          <div class="col-6">
            <div id="mini-chart4" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-success-subtle text-success">+2.95%</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col -->


  <div class="col-xl-2 col-md-6">
    <!-- card -->
    <div class="card card-h-100">
      <!-- card body -->
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-6">
            <span class="text-muted mb-3 lh-1 d-block text-truncate"><a href="/Invoices">Invoice</a></span>
            <h4 class="mb-3">
              $<span class="counter-value" data-target="74.3">0</span>K
            </h4>
          </div>
          <div class="col-6">
            <div id="mini-chart3" data-colors='["#5156be"]' class="apex-charts mb-2"></div>
          </div>
        </div>
        <div class="text-nowrap">
          <span class="badge bg-success-subtle text-success">+ $12.8k</span>
          <span class="ms-1 text-muted font-size-13">Since last week</span>
        </div>
      </div><!-- end card body -->
    </div><!-- end card -->
  </div><!-- end col -->


</div><!-- end row-->
<!-- 3rd row ends -->

<!-- Dashboard 4th row starts -->
<div class="row d-none">
  <div class="col-xl-6">
    <div class="card">
      <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">Recent Transactions</h4>
        <div class="flex-shrink-0">
          <ul class="nav justify-content-end nav-tabs-custom rounded card-header-tabs" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" data-bs-toggle="tab" href="#transactions-all-tab" role="tab">
                All
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" href="#transactions-buy-tab" role="tab">
                Low
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" href="#transactions-sell-tab" role="tab">
                High
              </a>
            </li>
          </ul>
          <!-- end nav tabs -->
        </div>
      </div><!-- end card header -->


      <div class="card-body px-0">
        <div class="tab-content">
          <div class="tab-pane active" id="transactions-all-tab" role="tabpanel">
            <div class="table-responsive px-3" data-simplebar style="max-height: 352px;">
              <table class="table align-middle table-nowrap table-borderless">
                <tbody>
                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$110.30</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">16 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$83.22</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">17 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$84.32</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">18 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$145.80</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$125.20</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- end tab pane -->
          <div class="tab-pane" id="transactions-buy-tab" role="tabpanel">
            <div class="table-responsive px-3" data-simplebar style="max-height: 352px;">
              <table class="table align-middle table-nowrap table-borderless">
                <tbody>
                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$125.20</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">18 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$145.80</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">16 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$94.22</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">$114.32</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">$93.32</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">$94.32</h5>
                        <p class="text-muted mb-0 font-size-12">17 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">$184.32</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$84.32</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-success">
                        <i class="bx bx-down-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$125.20</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>


                </tbody>
              </table>
            </div>
          </div>
          <!-- end tab pane -->
          <div class="tab-pane" id="transactions-sell-tab" role="tabpanel">
            <div class="table-responsive px-3" data-simplebar style="max-height: 352px;">
              <table class="table align-middle table-nowrap table-borderless">
                <tbody>
                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$125.20</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">18 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$145.80</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">15 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$112.34</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">Sell USD</h5>
                        <p class="text-muted mb-0 font-size-12">16 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$94.22</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>

                  <tr>
                    <td>
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">17 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$84.32</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>



                  <tr>
                    <td style="width: 50px;">
                      <div class="font-size-22 text-danger">
                        <i class="bx bx-up-arrow-circle d-block"></i>
                      </div>
                    </td>

                    <td>
                      <div>
                        <h5 class="font-size-14 mb-1">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">14 Mar, 2024</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 mb-0">May 2024</h5>
                        <p class="text-muted mb-0 font-size-12">Invoice Value</p>
                      </div>
                    </td>

                    <td>
                      <div class="text-end">
                        <h5 class="font-size-14 text-muted mb-0">$125.20</h5>
                        <p class="text-muted mb-0 font-size-12">Amount</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- end tab pane -->
        </div>
        <!-- end tab content -->
      </div>
      <!-- end card body -->
    </div>
    <!-- end card -->
  </div>
  <!-- end col -->


  <!-- task starts -->
  <div class="col-xl-6">
    <div class="card">
      <div class="card-header align-items-center d-flex">
        <h4 class="card-title mb-0 flex-grow-1">Recent Tasks</h4>
        <div class="flex-shrink-0">
          <select class="form-select form-select-sm mb-0 my-n1" id="task-filter">
            <option value="Today" selected="">Today</option>
            <option value="Yesterday">Yesterday</option>
            <option value="Week">Last Week</option>
            <option value="Month">Last Month</option>
          </select>
        </div>
      </div>
      <div class="card-body px-0">
        <div class="px-3" data-simplebar style="max-height: 352px;">
          <ul class="list-unstyled activity-wid mb-0" id="task-list"></ul>
        </div>
      </div>
    </div>
  </div>
  <!-- tasks ends -->
  
</div>
<!-- Dashboard 4th row ends -->





<%- contentFor('FooterJs') %>
<!-- apexcharts -->
<script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
<!-- Plugins js-->
<script src="/assets/libs/admin-resources/jquery.vectormap/jquery-jvectormap-1.2.2.min.js"></script>
<script src="/assets/libs/admin-resources/jquery.vectormap/maps/jquery-jvectormap-world-mill-en.js"></script>
<!-- dashboard init -->
<!-- <script src="/assets/js/pages/dashboard.init.js"></script> -->

<!-- Init js-->
<!-- <script src="/assets/js/pages/vector-maps.init.js"></script> -->


<!-- visitor script starts -->
<script>

var lastMonthVisitors=0;
var thisMonthVisitors=0;
var thisMonthVisitorTarget=10000;
var visitorTargetAchieved=0;
  
    // Radial Bar Chart
    const optionsRadialChart = {
        chart: { height: 270, type: "radialBar", offsetY: -10 },
        plotOptions: {
            radialBar: {
                startAngle: -130,
                endAngle: 130,
                dataLabels: {
                    name: { show: false },
                    value: { offsetY: 10, fontSize: "18px", formatter: val => `${val}%` }
                }
            }
        },
        colors: ["#5156be"],  // Set your own color here
        fill: {
            type: "gradient",
            gradient: {
                shade: "dark",
                type: "horizontal",
                gradientToColors: ["#775DD0"],  // Set your own gradient color here
                shadeIntensity: 0.15,
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [20, 60]
            }
        },
        stroke: { dashArray: 4 },
        legend: { show: false },
        series: [0], //default visitor meter percentage
        labels: ["Series A"]
    };
    chart = new ApexCharts(document.querySelector("#visitor-overview"), optionsRadialChart);
    chart.render();
  
  
  
  // Function to update the chart when visitorTargetAchieved changes
  function updateVisitorTargetAchieved(newTarget) {
      visitorTargetAchieved = newTarget;
      chart.updateSeries([visitorTargetAchieved]);
  }
  
  //updateVisitorTargetAchieved(visitorTargetAchieved)
    </script>
    <!-- visitor script ends -->
  
    

<script>


//update the chart updated target achieved
  $(document).ready(function() {

      // Payment Record Chart
      var optionsPaymentRecord = {
          series: [{
              name: 'Certificates',
              data: [30000, 40000, 35000, 50000, 49000, 60000, 70000, 91000, 125000, 150000, 160000, 180000]
          }],
          chart: {
              height: 300,
              type: 'line'
          },
          stroke: {
              width: 2,
              curve: 'smooth'
          },
          xaxis: {
              categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
          },
          colors: ['#556ee6']
      };
      var chartPaymentRecord = new ApexCharts(document.querySelector("#certificates-generated"), optionsPaymentRecord);


      chartPaymentRecord.render();

      // Total Sales Chart
      var optionsTotalSales = {
          series: [1200, 1450, 1250],
          chart: {
              height: 300,
              type: 'donut'
          },
          labels: ['Shopify eCommerce Store', 'iOS Apps Development', 'Figma Dashboard Design'],
          colors: ['#34c38f', '#f46a6a', '#50a5f1']
      };
      var chartTotalSales = new ApexCharts(document.querySelector("#certificates-generated"), optionsTotalSales);
      chartTotalSales.render();
  });
</script>


<!-- Payment Record start -->
    <script>


//update contacts count
$.ajax({
    url: '/api/contacts/analytics/count',
    type: 'GET',
    success: function(data) {
        //console.log('Total Contacts:', data.totalCount);
        // Update the UI or perform other actions with the count
        $("#contactCount").text(data.totalCount)
    },
    error: function(err) {
        console.error('Error fetching contact count:', err);
    }
});

//update certificate count
$.ajax({
    url: '/api/certificates/analytics/count',
    type: 'GET',
    success: function(data) {
        //console.log('Total Certificates:', data.totalCount);
        // Update the UI or perform other actions with the count

        $("#certificateCount").text(data.totalCount)


    },
    error: function(err) {
        console.error('Error fetching certificate count:', err);
    }
});


//update badge count
$.ajax({
    url: '/api/badges/analytics/count',
    type: 'GET',
    success: function(data) {
        //console.log('Total Badges:', data.totalCount);
        // Update the UI or perform other actions with the count

        $("#badgesCount").text(data.totalCount)


    },
    error: function(err) {
        console.error('Error fetching badges count:', err);
    }
});

//update badge count
$.ajax({
    url: '/api/users/analytics/count',
    type: 'GET',
    success: function(data) {
        //console.log('Total users:', data.totalCount);
        // Update the UI or perform other actions with the count

        $("#usersCount").text(data.totalCount)


    },
    error: function(err) {
        console.error('Error fetching users count:', err);
    }
});


$(document).ready(function() {
    // Function to update progress bars with counts
    function updateProgressBars() {
        // Define URLs for API requests
        const apiUrls = {
            plan: '/api/plans/active',
            contacts: '/api/contacts/analytics/count',
            certificates: '/api/certificates/analytics/count',
            badges: '/api/badges/analytics/count',
            users: '/api/users/analytics/count'
        };

        // Perform AJAX requests
        $.when(
            $.ajax({ url: apiUrls.plan, type: 'GET' }),
            $.ajax({ url: apiUrls.contacts, type: 'GET' }),
            $.ajax({ url: apiUrls.certificates, type: 'GET' }),
            $.ajax({ url: apiUrls.badges, type: 'GET' }),
            $.ajax({ url: apiUrls.users, type: 'GET' })
        ).done(function(plan, contactsData, certificatesData, badgesData, usersData) {
            
          //console.log("current plan",plan)
                  
        // Extract and typecast the total counts from the responses
        const contactLimit = plan[0].contactLimit;
        const contactCount = contactsData[0].totalCount;

        const certificateLimit = plan[0].certificateLimit;
        const certificateCount = certificatesData[0].totalCount;

        const badgeLimit = plan[0].badgeLimit;
        const badgeCount = badgesData[0].totalCount;

        const userLimit = plan[0].userLimit;
        const userCount = usersData[0].totalCount;

        //console.log(contactsData, certificatesData,badgesData, usersData )


        

        // Log the counts for debugging
        // console.log('Plan:', plan);
        // console.log('Total Contacts:', contactCount);
        // console.log('Total Certificates:', certificateCount);
        // console.log('Total Badges:', badgeCount);
        // console.log('Total Users:', userCount);


      // Update progress bars with the counts, rounding percentages to avoid decimal points
      $('#dashboard-contacts')
          .attr('aria-valuemax', contactLimit)
          .css('width', contactLimit > 0 ? Math.round((contactCount / contactLimit) * 100) + '%' : '0%')
          .attr('aria-valuenow', contactCount)
          .text(contactLimit > 0 ? Math.round((contactCount / contactLimit) * 100) + '%' : '0%');

      $('#dashboard-certificates')
          .attr('aria-valuemax', certificateLimit)
          .css('width', certificateLimit > 0 ? Math.round((certificateCount / certificateLimit) * 100) + '%' : '0%')
          .attr('aria-valuenow', certificateCount)
          .text(certificateLimit > 0 ? Math.round((certificateCount / certificateLimit) * 100) + '%' : '0%');

      $('#dashboard-badges')
          .attr('aria-valuemax', badgeLimit)
          .css('width', badgeLimit > 0 ? Math.round((badgeCount / badgeLimit) * 100) + '%' : '0%')
          .attr('aria-valuenow', badgeCount)
          .text(badgeLimit > 0 ? Math.round((badgeCount / badgeLimit) * 100) + '%' : '0%');

      $('#dashboard-users')
          .attr('aria-valuemax', userLimit)
          .css('width', userLimit > 0 ? Math.round((userCount / userLimit) * 100) + '%' : '0%')
          .attr('aria-valuenow', userCount)
          .text(userLimit > 0 ? Math.round((userCount / userLimit) * 100) + '%' : '0%');
              


        }).fail(function(err) {
            console.error('Error fetching data:', err);
        });
    }

    // Call the function to update progress bars on page load
    updateProgressBars();
});



//current active plan to calc the usage
$(document).ready(function() {
    // Fetch data from the API
    $.ajax({
        url: '/api/plans/active',
        type: 'GET',
        success: function(data) {
            
            const plan = data; 
            if (plan) {
                const { certificateLimit, userLimit, eventLimit } = plan;
                
              
              } else {
                console.error('No active plan found.');
            }
        },
        error: function(err) {
            console.error('Error fetching data:', err);
        }
    });
});



        $(document).ready(function() {
            var chartOptions = {
                series: [{
                    name: 'Certificate',
                    data: []
                }],
                chart: {
                    height: 350,
                    type: 'area',
                    dataColors: ["#5156be", "#34c38f"]
                },
                stroke: {
                    width: 2,
                    curve: 'smooth'
                },
                xaxis: {
                    categories: []
                },
                colors: ["#5156be", "#34c38f"]
            };

            var chart = new ApexCharts(document.querySelector("#certificates-generated"), chartOptions);
            chart.render();

            function updateChart(data, categories) {
                chart.updateOptions({
                    series: [{
                        data: data
                    }],
                    xaxis: {
                        categories: categories
                    }
                });
            }


            

            function getData(filter) {
                      var url = '/api/certificates/analytics/this-year';

                      switch (filter) {
                          case 'this-year':
                              url = '/api/certificates/analytics/this-year';
                              break;
                          case 'last-year':
                              url = '/api/certificates/analytics/last-year';
                              break;
                          case 'last-5-years':
                              url = '/api/certificates/analytics/last-5-years';
                              break;
                          case 'last-12-months':
                              url = '/api/certificates/analytics/last-12-months';
                              break;
                          case 'custom':
                              url = '/api/certificates/analytics/custom-date-range?startDate=2023-01-01&endDate=2023-12-31';
                              break;
                          default:
                              url = '/api/certificates/analytics/last-12-months';
                              break;
                      }

                      var result = {};
                      
                      $.ajax({
                          url: url,
                          method: 'GET',
                          async: false,  // This will make the request synchronous
                          success: function(response) {
                              result = response;
                          },
                          error: function() {
                              result = {
                                  data: [],
                                  categories: []
                              };
                          }
                      });

                      return result;
                  }




            function getDataStatic(filter) {
                switch (filter) {
                    case 'day':
                        return {
                            data: [100, 200, 150, 300, 250, 400, 350],
                            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                        };
                    case 'week':
                        return {
                            data: [700, 800, 750, 900, 850, 1000, 950],
                            categories: ['Week 1', 'Week 2', 'Week 3', 'Week 4']
                        };
                    case 'month':
                        return {
                            data: [3000, 4000, 3500, 5000, 4900, 6000, 7000, 9100, 12500, 15000, 16000, 18000],
                            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                        };
                    case 'quarter':
                        return {
                            data: [10000, 12000, 15000, 18000],
                            categories: ['Q1', 'Q2', 'Q3', 'Q4']
                        };
                        case 'year':
                          return {
                              data: [250000, 270000, 300000, 320000, 350000], // Example data for the last 5 years
                              categories: ['2020', '2021', '2022', '2023', '2024'] // Last 5 years
                          };

                    case 'custom':
                       return {
                            data: [3000, 4000, 3500, 5000, 4900, 6000, 7000, 9100, 12500, 15000, 16000, 18000],
                            categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
                        };
                    default:
                        return {
                            data: [],
                            categories: []
                        };
                }
            }

            

            $('.filter-button').on('change', function() {
    var filter = $(this).find('option:selected').data('filter');
    //alert(filter);
    var result = getData(filter);
    updateChart(result.data, result.categories);
});

            // Initialize with default data (e.g., month)
            var defaultData = getData('month');
            updateChart(defaultData.data, defaultData.categories);
        });
    </script>
<!-- Payment Record ends -->




<!-- update visitor range starts -->
<script>
  $(document).ready(function() {
    // Define the target for this month's visitors
    //const thisMonthVisitorTarget = 1000; // Example target value

    // Fetch the data using AJAX
    $.ajax({
        url: '/api/visitors/this-month',  // API endpoint
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            // Assuming the API response has the key "thisMonthVisitors"
            const thisMonthVisitors = response.thisMonthVisitors;

            // Calculate visitorTargetAchieved
            let visitorTargetAchieved = (thisMonthVisitors / thisMonthVisitorTarget) * 100;

            // Ensure the value is within the range 0-100%
            visitorTargetAchieved = Math.round(Math.min(Math.max(visitorTargetAchieved, 0), 100));


            //console.log("visitorTargetAchieved", visitorTargetAchieved, "thisMonthVisitors",thisMonthVisitors )
            // Call the existing function to update the chart
            updateVisitorTargetAchieved(visitorTargetAchieved);
        },
        error: function(xhr, status, error) {
            console.error("Error fetching data:", error);
            // Handle the error appropriately
        }
    });


});

</script>
<!-- update visitor range ends -->




<!-- visitor analytics starts -->
 <script>
  $(document).ready(function () {
    // Fetch and display last month's visitors
    $.ajax({
        url: '/api/visitors/last-month',
        type: 'GET',
        success: function (data) {

          //alert(data.lastMonthVisitors)

            $('#lastMonthVisitors').text(data.lastMonthVisitors);
             lastMonthVisitors=data.lastMonthVisitors;
             

        },
        error: function (err) {
            console.error('Error fetching last month visitors:', err);
            $('#lastMonthVisitors').text('Error');
        }
    });

    // Fetch and display this month's visitors
    $.ajax({
        url: '/api/visitors/this-month',
        type: 'GET',
        success: function (data) {
            $('#thisMonthVisitors').text(data.thisMonthVisitors);
            thisMonthVisitors=data.thisMonthVisitors;
        

        },
        error: function (err) {
            console.error('Error fetching this month visitors:', err);
            $('#thisMonthVisitors').text('Error');
        }
    });

    // Set the visitor target (this could also be dynamically fetched if needed)
    $('#visitorTarget').text('10,000'); // Example target value
});

 </script>
<!-- visitor analytics ends -->

<!-- tasks JS starts -->
<script>
  $(document).ready(function() {
    const tasks = [
      {
        icon: 'mdi-phone',
        bgColor: 'bg-warning-subtle',
        textColor: 'text-warning',
        title: 'Completed follow-up call',
        description: 'Completed follow-up call with John Smith at Google on 24/05/2024, 18:24:56',
        status: 'Done',
        amount: '$178.53'
      },
      {
        icon: 'mdi-email',
        bgColor: 'bg-primary-subtle',
        textColor: 'text-primary',
        title: 'Launched email campaign',
        description: 'Launched email campaign for Black Friday on 24/05/2024, 18:24:56',
        status: 'In Progress',
        amount: '$3541.45'
      },
      {
        icon: 'mdi-presentation',
        bgColor: 'bg-warning-subtle',
        textColor: 'text-warning',
        title: 'Delivered product demo',
        description: 'Delivered product demo for Apple Inc. on 24/05/2024, 18:24:56',
        status: 'Doing',
        amount: '$5791.45'
      },
      {
        icon: 'mdi-account-outline',
        bgColor: 'bg-primary-subtle',
        textColor: 'text-primary',
        title: 'Updated lead status',
        description: 'Updated lead status for prospect Jane Doe at Netflix on 24/05/2024, 18:24:56',
        status: 'Done',
        amount: '$5791.45'
      },
      {
        icon: 'mdi-chart-line',
        bgColor: 'bg-warning-subtle',
        textColor: 'text-warning',
        title: 'Reviewed monthly sales report',
        description: 'Reviewed monthly sales report with Microsoft team on 24/05/2024, 18:24:56',
        status: 'In Progress',
        amount: '$5791.45'
      },
      {
        icon: 'mdi-email-send',
        bgColor: 'bg-primary-subtle',
        textColor: 'text-primary',
        title: 'Sent thank you emails',
        description: 'Sent thank you emails to webinar attendees from IBM on 24/05/2024, 18:24:56',
        status: 'Done',
        amount: '$91.45'
      }
    ];

    function renderTasks() {
      const $taskList = $('#task-list');
      $taskList.empty();
      tasks.forEach(task => {
        const taskHtml = `
          <li class="activity-list activity-border">
            <div class="activity-icon avatar-md">
              <span class="avatar-title ${task.bgColor} ${task.textColor} rounded-circle">
                <i class="mdi ${task.icon} font-size-24"></i>
              </span>
            </div>
            <div class="timeline-list-item">
              <div class="d-flex">
                <div class="flex-grow-1 overflow-hidden me-4">
                  <h2 class="font-size-14 mb-1">${task.title}</h2>
                  <p class="text-truncate text-muted font-size-13">${task.description}</p>
                </div>
                <div class="flex-shrink-0 text-end me-3">
                  <h6 class="mb-1">${task.status}</h6>
                  <div class="font-size-13">${task.amount}</div>
                </div>
                <div class="flex-shrink-0 text-end">
                  <div class="dropdown">
                    <a class="text-muted dropdown-toggle font-size-24" role="button" data-bs-toggle="dropdown" aria-haspopup="true">
                      <i class="mdi mdi-dots-vertical"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        `;
        $taskList.append(taskHtml);
      });
    }

    renderTasks();
  });
</script>

<!-- tasks JS ends -->


